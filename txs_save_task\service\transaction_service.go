package service

import (
	"time"

	log "github.com/cihub/seelog"
	"github.com/tokenbankteam/txs_save_task/config"
	"github.com/tokenbankteam/txs_save_task/model"
	"github.com/tokenbankteam/txs_save_task/model/transaction"
)

type TransactionService struct {
	AppConfig        *config.AppConfig
	TransactionModel *transaction.Model
}

func NewTransactionService(context *AppContext) (*TransactionService, error) {
	return &TransactionService{
		AppConfig:        context.Config,
		TransactionModel: context.Models["transactionModel"].(*transaction.Model),
	}, nil
}

func (s *TransactionService) InsertOrUpdateTransactionList(transactionList []*model.Transaction) (interface{}, error) {
	start1 := time.Now().Nanosecond()
	defer func() {
		elapsed := (time.Now().Nanosecond() - start1) / (1000 * 1000)
		if elapsed > 100 {
			// 将时间戳转换为UTC+8时间
			utc8Time := time.Unix(int64(transactionList[0].Timestamp), 0).In(time.FixedZone("UTC+8", 8*3600))
			log.Infof("InsertOrUpdateTransactionList id %v date:%v, elapsed %vms", transactionList[0].Hash, utc8Time.Format("2006-01-02 15:04:05"), elapsed)
		}
	}()
	return s.TransactionModel.InsertOrUpdateTransactionList(transactionList)
}

func (s *TransactionService) UpdateTransactionForkInfo(msg *model.ForkMsg) (interface{}, error) {
	start1 := time.Now().Nanosecond()
	defer func() {
		elapsed := (time.Now().Nanosecond() - start1) / (1000 * 1000)
		if elapsed > 100 {
			log.Infof("UpdateTransactionForkInfo id %v, elapsed %vms", msg, elapsed)
		}
	}()
	return s.TransactionModel.UpdateTransactionForkInfo(msg)
}
