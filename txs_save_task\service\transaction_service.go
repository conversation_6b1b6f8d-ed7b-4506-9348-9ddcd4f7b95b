package service

import (
	log "github.com/cihub/seelog"
	"github.com/tokenbankteam/txs_save_task/config"
	"github.com/tokenbankteam/txs_save_task/model"
	"github.com/tokenbankteam/txs_save_task/model/transaction"
	"time"
)

type TransactionService struct {
	AppConfig        *config.AppConfig
	TransactionModel *transaction.Model
}

func NewTransactionService(context *AppContext) (*TransactionService, error) {
	return &TransactionService{
		AppConfig:        context.Config,
		TransactionModel: context.Models["transactionModel"].(*transaction.Model),
	}, nil
}

func (s *TransactionService) InsertOrUpdateTransactionList(transactionList []*model.Transaction) (interface{}, error) {
	start1 := time.Now().Nanosecond()
	defer func() {
		elapsed := (time.Now().Nanosecond() - start1) / (1000 * 1000)
		if elapsed > 100 {
			log.Infof("InsertOrUpdateTransactionList id %v, elapsed %vms", transactionList[0].Hash, elapsed)
		}
	}()
	return s.TransactionModel.InsertOrUpdateTransactionList(transactionList)
}

func (s *TransactionService) UpdateTransactionForkInfo(msg *model.ForkMsg) (interface{}, error) {
	start1 := time.Now().Nanosecond()
	defer func() {
		elapsed := (time.Now().Nanosecond() - start1) / (1000 * 1000)
		if elapsed > 100 {
			log.Infof("UpdateTransactionForkInfo id %v, elapsed %vms", msg, elapsed)
		}
	}()
	return s.TransactionModel.UpdateTransactionForkInfo(msg)
}
