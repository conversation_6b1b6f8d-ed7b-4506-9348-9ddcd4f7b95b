package model

import (
	"fmt"
)

type ContractAddressInfo struct {
	BlockChainId int64    `json:"blockchain_id"`
	Protocol     int64    `json:"protocol"`
	Token        string   `json:"token"`
	BlSymbol     string   `json:"bl_symbol"`
	Ts           int64    `json:"ts"`
	TokenIds     []string `json:"token_ids"`
}

func (c ContractAddressInfo) GenerateCacheKey() string {
	return fmt.Sprintf("%v_%v_%v", c.<PERSON>d, c.<PERSON>, c.Token<PERSON>)
}
