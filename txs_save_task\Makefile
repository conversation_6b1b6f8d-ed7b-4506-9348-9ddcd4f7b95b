TARGET=full-savetrx-task-base
SOURCES=$(wildcard *.go)
VERSION ?= $(shell git describe --always --long --dirty --tags)

IMAGE=registry.cn-hongkong.aliyuncs.com/tokenpocket/${TARGET}:${VERSION}
IMAGE_TEST=registry.cn-hongkong.aliyuncs.com/tokenpocket/${TARGET}-test:${VERSION}

$(TARGET): $(SOURCES)
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-X main.version=${VERSION}" -o $@ $(SOURCES)

.PHONY: clean
clean:
	rm -f $(TARGET)

vendor: glide.yaml
	glide update

.PHONY: docker
docker:
	docker build -f ./Dockerfile -t $(IMAGE) --build-arg TARGET=$(TARGET) .

.PHONY: docker-push
docker-push:
	docker push $(IMAGE)

TIME ?= $(shell date +%Y%m%d%H%M%S)
TMP_DP_FILE = k8s/tmp/tmp.dp.yaml.$(TIME)
TMP_DP_FILE_APPLY = k8s/tmp/tmp.dp.yaml.apply.$(TIME)
TMP_CM_FILE = k8s/tmp/tmp.cm.yaml.$(TIME)

.PHONY: apply
apply:
	mkdir -p k8s/tmp
	sed 's/TPIMAGE_VERSION/${VERSION}/g' k8s/dp-prod.yaml > $(TMP_DP_FILE)
	sed 's/TP_TARGET/${TARGET}/g' $(TMP_DP_FILE) > $(TMP_DP_FILE_APPLY)
	k8s_prod apply -f $(TMP_DP_FILE_APPLY)
	date

.PHONY: stop
stop:
	k8s_prod scale deploy ${TARGET} -n tp-bserver --replicas=0
	date

.PHONY: delete-server
delete-server:
	mkdir -p k8s/tmp
	sed 's/TP_TARGET/${TARGET}/g' k8s/cm.yaml > $(TMP_CM_FILE)
	k8s_prod apply -f $(TMP_CM_FILE)
	sed 's/TPIMAGE_VERSION/${VERSION}/g' k8s/dp-prod.yaml > $(TMP_DP_FILE)
	sed 's/TP_TARGET/${TARGET}/g' $(TMP_DP_FILE) > $(TMP_DP_FILE_APPLY)
	k8s_prod delete -f $(TMP_DP_FILE_APPLY)
	date

.PHONY: all
all:
	make docker
	make docker-push
	date

.PHONY: apply-test
apply-test:
	make clean && make
	cp ${TARGET} ${TARGET}-test
	docker build -f ./Dockerfile -t $(IMAGE_TEST) --build-arg TARGET=$(TARGET)-test .
	docker push $(IMAGE_TEST)
	mkdir -p k8s/tmp
	sed 's/TP_TARGET/${TARGET}/g' k8s/cm.yaml > $(TMP_CM_FILE)
	k8s_prod apply -f $(TMP_CM_FILE)
	sed 's/TPIMAGE_VERSION/${VERSION}/g' k8s/dp.yaml > $(TMP_DP_FILE)
	sed 's/TP_TARGET/${TARGET}/g' $(TMP_DP_FILE) > $(TMP_DP_FILE_APPLY)
	k8s_prod apply -f $(TMP_DP_FILE_APPLY)
	date

.PHONY: apply-release
apply-release:
	make clean && make
	docker build -f ./Dockerfile -t $(IMAGE) --build-arg TARGET=$(TARGET) .
	docker push $(IMAGE)
	mkdir -p k8s/tmp
	sed 's/TP_TARGET/${TARGET}/g' k8s/cm-prod.yaml > $(TMP_CM_FILE)
	k8s_prod apply -f $(TMP_CM_FILE)
	sed 's/TPIMAGE_VERSION/${VERSION}/g' k8s/dp-prod.yaml > $(TMP_DP_FILE)
	sed 's/TP_TARGET/${TARGET}/g' $(TMP_DP_FILE) > $(TMP_DP_FILE_APPLY)
	k8s_prod apply -f $(TMP_DP_FILE_APPLY)
	date