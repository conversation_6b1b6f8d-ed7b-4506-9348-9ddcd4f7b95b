# 系统环境
profile: dev

# 是否监控
monitor: true
# PprofAddrs *************:7272,********:7272
monitoraddrs: [ '0.0.0.0:15574' ]

# PprofAddrs *************:7271,********:7271
pprofaddrs: [ '0.0.0.0:15573' ]

# 服务监听地址
addr: '0.0.0.0:15575'

pushgateway: 'http://tppushgateway.mytokenpocket.vip'
pushjobname: 'task_save_trx_linea_full'

# 日志配置文件
logger: config/logger.xml

# 数据库配置
db:
  #实例列表
  instances:
    master:
      driver: mysql
      url: root:G6W9$NqTFxYy@tcp(*************:3306)/mc_transaction?timeout=3s&readTimeout=3s&writeTimeout=3s&parseTime=true
    lock_master:
      driver: mysql
      url: root:G6W9$NqTFxYy@tcp(*************:3306)/mc_transaction?timeout=3s&readTimeout=3s&writeTimeout=3s&parseTime=true

# 缓存配置
redis:
  #实例列表
  instances:
    master:
      url: server.redis.com:6379
      password: ""

mongoaddr: 'mgs1internal.mytokenpocket.vip:27017,mgs2internal.mytokenpocket.vip:27017,mgs3internal.mytokenpocket.vip:27017'
database: 'base'
collection: 'full_txs'
pending: false

brokerlist: [ '*************:9092', '*************:9092', '*************:9092' ]
trxsavetopic: 'base_direct_trx_full'
wallettokentopic: 'wallet_token_topic_test'
trxsaveconsumergroup: 'test'
blockchain: 'base'
blockchainid: 51