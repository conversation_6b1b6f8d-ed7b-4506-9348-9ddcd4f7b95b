kind: ConfigMap
apiVersion: v1
metadata:
  name: cm-TP_TARGET-test
  namespace: tp-bserver
data:
  config_prod.yml: |
    # 系统环境
    profile: dev

    # 是否监控
    monitor: true
    # PprofAddrs *************:7272,********:7272
    monitoraddrs: [ '0.0.0.0:5574' ]

    # PprofAddrs *************:7271,********:7271
    pprofaddrs: [ '0.0.0.0:5573' ]

    # 服务监听地址
    addr: '0.0.0.0:5575'

    pushgateway: 'http://tppushgateway.mytokenpocket.vip'
    pushjobname: 'task_save_trx_bsc'

    # 日志配置文件
    logger: config/logger.xml

    mongoaddr: 'mongodb://mgs1internal.mytokenpocket.vip:27017,mgs2internal.mytokenpocket.vip:27017,mgs3internal.mytokenpocket.vip:27017/?readPreference=secondaryPreferred'
    database: 'bsc'
    pendingdatabase: 'bscpending'
    pendingdatabasecollection: 'transaction'
    pendingdatabasecollectionv2: 'transaction2'
    pending: true

    brokerlist: [ '*************:9092', '*************:9092', '*************:9092' ]
    trxsavetopic: 'bsc_trx_v2'
    directtrxtopic: 'bsc_direct_trx_v2'
    trxsaveconsumergroup: 'kafka_group_test'
    pendingtrxtopic: 'ethereum_trx_pending'
    pendingtoleranceintervaltime: 3
    blockchain: 'bsc'
    blockchainid: 12

    mappingconfig:
      mongoaddr: 'mongodb://mgs1internal.mytokenpocket.vip:27017,mgs2internal.mytokenpocket.vip:27017,mgs3internal.mytokenpocket.vip:27017/?readPreference=secondaryPreferred'
      database: 'mapping'
      col: 'addr'
      gidaddr: 'http://gid-srv:8082'
      cachesize: 204800
      expire: 3600