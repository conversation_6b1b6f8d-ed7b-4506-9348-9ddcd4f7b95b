# Task Document

## Title

a evm transaction saver

store layer: mongodb

[数据来源](doc/dataRes.md)

> 注意: 全量数据包含冷热分离逻辑，需要写daily_block

### 编译

    make

# 部署

## k8s

```
//打包docker
make docker

//上传docker
make docker-push

//部署docker到k8s 需用到k8s_prod,k8s_prod为kubectl对应的软连接
make apply
```

停止服务

```
//调整pod为0 即停止服务 需用到k8s_prod,k8s_prod为kubectl对应的软连接
make stop
```

删除服务

```
//删除dp 需用到k8s_prod,k8s_prod为kubectl对应的软连接
make delete-server
```
# attach

k8s_prod run curl-test --image=radial/busyboxplus:curl -i --tty --rm -n tp-bserver

## Desc

```

//创建存储入块表 prod
# use admin;
# db.runCommand( { enablesharding :"base"});
# use base;
# db.createCollection("full_txs");

# use admin;
# db.runCommand( { shardcollection : "base.full_txs", key : {n: 1,b: 1, h: 1}, unique: true } );
# use base;
# db.full_txs.createIndex({h: 1});
# db.full_txs.createIndex({"l.f": 1, "l.a": 1, t: -1});
# db.full_txs.createIndex({"l.t": 1, "l.a": 1, t: -1});
# db.full_txs.createIndex({"l.a": 1, "l.z": 1, t: -1});
# db.full_txs.getIndexes();
```