package task

import (
	log "github.com/cihub/seelog"
	"github.com/tokenbankteam/txs_save_task/config"
	"github.com/tokenbankteam/txs_save_task/service"
)

type Manager struct {
	AppContext *service.AppContext
	Config     *config.AppConfig

	blockChainSyncTask *BlockChainSyncTask
}

func NewTaskManager(config *config.AppConfig, appContext *service.AppContext) (*Manager, error) {
	manager := &Manager{
		AppContext: appContext,
		Config:     config,
	}
	return manager, nil
}

func (s *Manager) init() {
}

// 启动定时任务管理器
func (s *Manager) Start() {
	s.init()

	blockChainSyncTask, err := NewBlockChainSyncTask(s)
	if err != nil {
		log.Errorf("new blockChainSync task error, %v", err)
		return
	}
	s.blockChainSyncTask = blockChainSyncTask
	s.blockChainSyncTask.Start()
}
