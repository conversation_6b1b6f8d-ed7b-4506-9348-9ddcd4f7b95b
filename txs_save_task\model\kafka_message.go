package model

import (
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/core/types"
	"math/big"
)

type NodeTransaction struct {
	AccountNonce uint64          `json:"nonce"`
	Price        string          `json:"gasPrice"`
	BaseFee      string          `json:"baseFee"`
	GasTipCap    string          `json:"gasTipCap"`
	GasFeeCap    string          `json:"gasFeeCap"`
	GasLimit     uint64          `json:"gas"`
	Recipient    *common.Address `json:"to"` // nil means contract creation
	Amount       string          `json:"value"`
	Payload      []byte          `json:"input"`
	// This is only used when marshaling to JSON.
	Hash *common.Hash `json:"hash"`
}

type Log struct {
	// Consensus fields:
	// address of the contract that generated the event
	Address common.Address `json:"address"`
	// list of topics provided by the contract.
	Topics []common.Hash `json:"topics"`
	// supplied by the contract, usually ABI-encoded
	Data []byte `json:"data"`

	// Derived fields. These fields are filled in by the node
	// but not secured by consensus.
	// block in which the transaction was included
	BlockNumber uint64 `json:"blockNumber"`
	// hash of the transaction
	TxHash common.Hash `json:"transactionHash"`
	// index of the transaction in the block
	TxIndex uint `json:"transactionIndex"`
	// hash of the block in which the transaction was included
	BlockHash common.Hash `json:"blockHash"`
	// index of the log in the block
	Index uint `json:"logIndex"`

	// The Removed field is true if this log was reverted due to a chain reorganisation.
	// You must pay attention to this field if you receive logs through a filter query.
	Removed bool `json:"removed"`
}

type Receipt struct {
	// Consensus fields: These fields are defined by the Yellow Paper
	PostState         []byte      `json:"root"`
	Status            uint64      `json:"status"`
	CumulativeGasUsed uint64      `json:"cumulativeGasUsed"`
	Bloom             types.Bloom `json:"logsBloom"`
	Logs              []*Log      `json:"logs"`

	// Implementation fields: These fields are added by geth when processing a transaction.
	// They are stored in the chain database.
	TxHash          common.Hash    `json:"transactionHash"`
	ContractAddress common.Address `json:"contractAddress"`
	GasUsed         uint64         `json:"gasUsed"`

	// Inclusion information: These fields provide information about the inclusion of the
	// transaction corresponding to this receipt.
	BlockHash        common.Hash `json:"blockHash,omitempty"`
	BlockNumber      uint64      `json:"blockNumber,omitempty"`
	TransactionIndex uint        `json:"transactionIndex"`
	// UsingOVM
	L1GasPrice          *big.Int   `json:"l1GasPrice" gencodec:"required"`
	L1GasUsed           *big.Int   `json:"l1GasUsed" gencodec:"required"`
	L1Fee               *big.Int   `json:"l1Fee" gencodec:"required"`
	FeeScalar           *big.Float `json:"l1FeeScalar" gencodec:"required"`
	L1BaseFeeScalar     *uint64    `json:"l1BaseFeeScalar,omitempty"`     // Always nil prior to the Ecotone hardfork
	L1BlobBaseFeeScalar *uint64    `json:"l1BlobBaseFeeScalar,omitempty"` // Always nil prior to the Ecotone hardfork

	OperatorFeeScalar   *uint64 `json:"operatorFeeScalar,omitempty"`   // Always nil prior to the Isthmus hardfork
	OperatorFeeConstant *uint64 `json:"operatorFeeConstant,omitempty"` // Always nil prior to the Isthmus hardfork
}

type TransactionAndInternalTx struct {
	Transaction      *NodeTransaction `json:"transaction"`
	From             common.Address   `json:"from"`
	TransactionIndex uint64           `json:"TransactionIndex"`
	BlockNumber      uint64           `json:"blockNumber"`
	Timestamp        uint64           `json:"timestamp"`
	ParentBlockHash  common.Hash      `json:"parentBlockHash"`
	BlockHash        common.Hash      `json:"blockHash"`
	InternalTxList   []InternalTx     `json:"internalTxList"`
	Receipt          *Receipt         `json:"receipt"`
	ErrorMessage     string           `json:"errorMessage"`
	ErrorReason      string           `json:"errorReason"`
}

func (s *TransactionAndInternalTx) ParseErrReason() string {
	errRet, err := hexutil.Decode(s.ErrorReason)
	if err != nil {
		return ""
	}
	if len(errRet) > 68 {
		prefix := hexutil.Encode(errRet[:4])
		if "0x08c379a0" == prefix {
			for i := 68 + 1; i < len(errRet); i++ {
				if int64(errRet[i]) == 0 {
					return string(errRet[68:i])
				}
			}
			return string(errRet[68:])
		}
	}
	return ""
}

// InternalTx for the internal tx of a contract transaction
type InternalTx struct {
	BlockNumber  uint64      // the number of the block where the internal tx lies
	BlockHash    common.Hash // the hash of the block where the internal tx lies
	ParentTxHash common.Hash // the paranet hash of the internal tx
	CallType     string      // the call type of the internal tx
	Path         string
	From         common.Address // the from address of the internal tx
	To           common.Address // the to address of the internal tx
	GasLimit     uint64         // the gas limit of the internal tx
	Value        string         // the value of the internal tx
	Input        []byte
}

type Block struct {
	Number                uint64          `json:"number"`
	Hash                  string          `json:"hash"`
	ParentHash            string          `json:"parentHash"`
	ConfirmedBlockForkMap map[string]bool `json:"confirmedBlockForkMap"`
	ConfirmedBlockNumber  uint64          `json:"confirmedBlockNumber"`
}

type TrxMessageBody struct {
	BlockChain               string                    `json:"blockchain"`
	TransactionAndInternalTx *TransactionAndInternalTx `json:"ethereum_tx"`
	Action                   int64                     `json:"action"`
	TxForkMsg                *ForkMsg                  `json:"fork_msg"`
}

type ForkMsg struct {
	Fork      bool   `json:"fork"`
	TxHash    string `json:"tx_hash"`
	BlockHash string `json:"block_hash"`
}
