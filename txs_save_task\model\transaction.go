package model

import (
	"github.com/ethereum/go-ethereum/common"
	"go.mongodb.org/mongo-driver/bson"
)

// Transaction TransactionDoc 唯一索引 blockNumber + blockHash + hash + logIndex + internalIndex
type Transaction struct {
	BlockHash        common.Hash `bson:"b"` // tx blockHash
	BlockNumber      int32       `bson:"n"` // 区块号
	TransactionIndex int32       `bson:"x"`
	Timestamp        int32       `bson:"t"` // 交易时间
	Hash             common.Hash `bson:"h"` // tx id
	Nonce            int32       `bson:"o"` // tx nonce
	Input            []byte      `bson:"q"`
	Gas              uint64      `bson:"g,omitempty"` // gas
	GasPrice         string      `bson:"p,omitempty"` // gas price
	BaseFee          string      `bson:"e,omitempty"`
	GasTipCap        string      `bson:"c,omitempty"` //小费
	GasFeeCap        string      `bson:"j,omitempty"` //小费+baseFee*2  这里的baseFee预期，不是上面的baseFee
	UsedGas          uint64      `bson:"w,omitempty"` // used gas
	Status           int32       `bson:"u"`           //1 (success) or 0 (failure) or 2(pending), 99未知
	Fork             bool        `bson:"k,omitempty"`
	ErrorMessage     string      `bson:"m,omitempty"`
	ErrorReason      string      `bson:"r,omitempty"`

	L1GasPrice string  `bson:"s,omitempty"`
	L1GasUsed  int64   `bson:"v,omitempty"`
	L1Fee      string  `bson:"y,omitempty"`
	FeeScalar  float32 `bson:"z,omitempty"`

	OperatorFeeScalar   uint64 `bson:"o1,omitempty"` // Always nil prior to the Isthmus hardfork
	OperatorFeeConstant uint64 `bson:"o2,omitempty"` // Always nil prior to the Isthmus hardfork

	TransferList []Transfer `bson:"l"`
}

type Transfer struct {
	AddrList      []common.Address `bson:"m"`
	From          common.Address   `bson:"f,omitempty"`
	To            common.Address   `bson:"t,omitempty"`
	AddrToken     common.Address   `bson:"a,omitempty"` //  代币地址
	InternalIndex string           `bson:"i"`           //字符串处理，　默认是-1, 第一层0, 第二层0_0,
	LogIndex      int32            `bson:"l"`
	Value         string           `bson:"v"` // eth number
	TokenId       string           `bson:"z,omitempty"`
	//GasLimit      uint64           `bson:"g,omitempty"` //内部交易的gasLimit
	GasLimit     string `bson:"g1,omitempty"` //内部交易的gasLimit
	Error        string `bson:"e,omitempty"`
	RevertReason string `bson:"r,omitempty"`
}

func (s *Transaction) GetUniqueKeys() bson.M {
	return bson.M{"n": s.BlockNumber, "b": s.BlockHash, "h": s.Hash}
}
