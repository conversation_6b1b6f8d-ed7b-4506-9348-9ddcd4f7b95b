package transaction

import (
	"encoding/json"
	"fmt"
	"github.com/Shopify/sarama"
	cluster "github.com/bsm/sarama-cluster"
	"github.com/tokenbankteam/txs_save_task/model"
	"gotest.tools/assert"
	"math/big"
	"os"
	"os/signal"
	"syscall"
	"testing"
)

func TestUnmarshalData(t *testing.T) {
	newInt := big.NewInt(0)
	newInt.UnmarshalJSON([]byte("0x000000000000000000000000000000000000000000000000165973173c965ad5"))
	fmt.Println(fmt.Sprintf("%v", newInt.String()))
}

func TestWalletToken(t *testing.T) {
	// init (custom) config, enable errors and notifications
	kafkaConfig := cluster.NewConfig()
	kafkaConfig.Group.Return.Notifications = true
	kafkaConfig.Consumer.Offsets.Initial = sarama.OffsetOldest

	consumer, err := cluster.NewConsumer([]string{"172.31.233.86:9092", "172.31.233.87:9092", "172.31.233.88:9092"}, "full_direct_nft_op_test", []string{"wallet_token_topic"}, kafkaConfig)
	assert.NilError(t, err, "")
	// trap SIGINT to trigger a shutdown.
	signals := make(chan os.Signal, 1)
	signal.Notify(signals, os.Interrupt, syscall.SIGTERM)

	// consume notifications
	go func() {
		for ntf := range consumer.Notifications() {
			marshal, _ := json.Marshal(ntf)
			fmt.Printf("!Rebalanced notifications: %v", string(marshal))
		}
	}()
	for {
		select {
		case msg, ok := <-consumer.Messages():
			if ok && msg != nil {
				var res []*model.ContractAddressInfo
				json.Unmarshal(msg.Value, &res)
				for i, info := range res {
					if info.BlockChainId != 51 {
						break
					}
					fmt.Println(i, info)
				}
				consumer.MarkOffset(msg, "") // mark message as processed
			}
		case <-signals:
			fmt.Printf("receive signals ,stop.....")
			return
		}
	}
}
