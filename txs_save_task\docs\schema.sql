CREATE DATABASE `mc_transaction` DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;

DROP TABLE IF EXISTS `t_resource_lock`;
CREATE TABLE `t_resource_lock` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `lock_name` varchar(64) NOT NULL DEFAULT '' COMMENT '锁名字',
  `lock_code` varchar(64) NOT NULL DEFAULT '' COMMENT '锁代码',
  `lock_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '锁定时间， 单位毫秒',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uqi_lock_name` (`lock_name`),
  UNIQUE KEY `uqi_lock_code` (`lock_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='资源锁表';

DROP TABLE IF EXISTS `t_ethtx`;
CREATE TABLE `t_ethtx` (
  `ttimestamp` bigint(20) NOT NULL DEFAULT '0' COMMENT '时间戳',
  `blocknumber` bigint(20) NOT NULL DEFAULT '0' COMMENT '块编号',
  `addr_from` VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'from地址',
  `addr_to` VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'to地址',
  `gas` VARCHAR(256) NOT NULL DEFAULT '0' COMMENT 'gas',
  `gas_used` VARCHAR(256) NOT NULL DEFAULT '0' COMMENT 'gas used',
  `gas_price` VARCHAR(256) NOT NULL DEFAULT '0' COMMENT 'gas price',
  `value` VARCHAR(256) NOT NULL DEFAULT '0' COMMENT '值',
  `hash` VARCHAR(66) NOT NULL DEFAULT '' COMMENT 'hash值',
  `type` int NOT NULL DEFAULT '0' COMMENT '类型',
  `status` int NOT NULL DEFAULT '0' COMMENT '状态 1表示success, 0表示failure, 2表示pending',
  `method_id` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '方法id',
  `addr_token` VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'token合约地址',
  `token_value` VARCHAR(256) NOT NULL DEFAULT '0' COMMENT 'token值',
  `input` VARCHAR(4096) NOT NULL DEFAULT '' COMMENT 'input参数',
  PRIMARY KEY (`hash`),
  KEY `idx_ttimestamp` (`ttimestamp`),
  KEY `idx_blocknumber` (`blocknumber`),
  KEY `idx_hash` (`hash`),
  KEY `idx_type` (`type`),
  KEY `idx_addr_from` (`addr_from`),
  KEY `idx_addr_to` (`addr_to`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='以太交易记录表';

DROP TABLE IF EXISTS `t_eth_statistics_1min`;
CREATE TABLE `t_eth_statistics_1min` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '时间戳',
  `start_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '结束时间',
  `sum_value` DECIMAL(65, 18) NOT NULL DEFAULT '0' COMMENT '总值',
  `sum_token_value` DECIMAL(65, 18) NOT NULL DEFAULT '0' COMMENT '总token值',
  `type` TINYINT NOT NULL DEFAULT '0' COMMENT '类型',
  `addr_token` VARCHAR(42) NOT NULL DEFAULT '' COMMENT 'token合约地址',
  `addr_to` VARCHAR(42) NOT NULL DEFAULT '' COMMENT 'to地址',
  `sum_gas` DECIMAL(65, 18) NOT NULL DEFAULT '0' COMMENT '总gas值',
  `interval` SMALLINT NOT NULL DEFAULT '0' COMMENT '间隔时间',
  PRIMARY KEY (`id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_addr_to` (`addr_to`),
  KEY `idx_addr_token` (`addr_token`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='以太1分钟统计表';

DROP TABLE IF EXISTS `t_eth_statistics_5min`;
CREATE TABLE `t_eth_statistics_5min` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '时间戳',
  `start_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '结束时间',
  `sum_value` DECIMAL(65, 18) NOT NULL DEFAULT '0' COMMENT '总值',
  `sum_token_value` DECIMAL(65, 18) NOT NULL DEFAULT '0' COMMENT '总token值',
  `type` TINYINT NOT NULL DEFAULT '0' COMMENT '类型',
  `addr_token` VARCHAR(42) NOT NULL DEFAULT '' COMMENT 'token合约地址',
  `addr_to` VARCHAR(42) NOT NULL DEFAULT '' COMMENT 'to地址',
  `sum_gas` DECIMAL(65, 18) NOT NULL DEFAULT '0' COMMENT '总gas值',
  `interval` SMALLINT NOT NULL DEFAULT '0' COMMENT '间隔时间',
  PRIMARY KEY (`id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_addr_to` (`addr_to`),
  KEY `idx_addr_token` (`addr_token`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='以太5分钟统计表';

DROP TABLE IF EXISTS `t_eth_statistics_10min`;
CREATE TABLE `t_eth_statistics_10min` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '时间戳',
  `start_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '结束时间',
  `sum_value` DECIMAL(65, 18) NOT NULL DEFAULT '0' COMMENT '总值',
  `sum_token_value` DECIMAL(65, 18) NOT NULL DEFAULT '0' COMMENT '总token值',
  `type` TINYINT NOT NULL DEFAULT '0' COMMENT '类型',
  `addr_token` VARCHAR(42) NOT NULL DEFAULT '' COMMENT 'token合约地址',
  `addr_to` VARCHAR(42) NOT NULL DEFAULT '' COMMENT 'to地址',
  `sum_gas` DECIMAL(65, 18) NOT NULL DEFAULT '0' COMMENT '总gas值',
  `interval` SMALLINT NOT NULL DEFAULT '0' COMMENT '间隔时间',
  PRIMARY KEY (`id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_addr_to` (`addr_to`),
  KEY `idx_addr_token` (`addr_token`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='以太10分钟统计表';

DROP TABLE IF EXISTS `t_eth_statistics_30min`;
CREATE TABLE `t_eth_statistics_30min` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '时间戳',
  `start_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '结束时间',
  `sum_value` DECIMAL(65, 18) NOT NULL DEFAULT '0' COMMENT '总值',
  `sum_token_value` DECIMAL(65, 18) NOT NULL DEFAULT '0' COMMENT '总token值',
  `type` TINYINT NOT NULL DEFAULT '0' COMMENT '类型',
  `addr_token` VARCHAR(42) NOT NULL DEFAULT '' COMMENT 'token合约地址',
  `addr_to` VARCHAR(42) NOT NULL DEFAULT '' COMMENT 'to地址',
  `sum_gas` DECIMAL(65, 18) NOT NULL DEFAULT '0' COMMENT '总gas值',
  `interval` SMALLINT NOT NULL DEFAULT '0' COMMENT '间隔时间',
  PRIMARY KEY (`id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_addr_to` (`addr_to`),
  KEY `idx_addr_token` (`addr_token`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='以太30分钟统计表';

DROP TABLE IF EXISTS `t_eth_token_history`;
CREATE TABLE `t_eth_token_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `address` VARCHAR(42) NOT NULL DEFAULT '' COMMENT '钱包地址',
  `contract_address` VARCHAR(42) NOT NULL DEFAULT '' COMMENT '合约地址',
  `value` DECIMAL(65, 18) NOT NULL DEFAULT '0' COMMENT '数量',
  `price` DECIMAL(65, 18) NOT NULL DEFAULT '0' COMMENT '价格',
  `timestamp` bigint(20) NOT NULL DEFAULT '0' COMMENT '时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_address_contract_address_timestamp` (`address`, `contract_address`, `timestamp`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='以太坊，代币历史数据';
