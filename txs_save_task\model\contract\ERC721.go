// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package contract

import (
	"errors"
	"math/big"
	"strings"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/event"
)

// Reference imports to suppress errors if they are not otherwise used.
var (
	_ = big.NewInt
	_ = strings.NewReader
	_ = ethereum.NotFound
	_ = bind.Bind
	_ = common.Big1
	_ = types.BloomLookup
	_ = event.NewSubscription
)

// ERC721ABI is the input ABI used to generate the binding from.
const ERC721ABI = "[{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"presaleAmount\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"_baseURI\",\"type\":\"string\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"approved\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"ApprovalForAll\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"GoatPresale\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"baseURI\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"nft\",\"type\":\"uint256\"}],\"name\":\"buy\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"nft\",\"type\":\"uint256\"}],\"name\":\"buy_presale\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"calledWithdrawES\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_sumon_token_id\",\"type\":\"uint256\"}],\"name\":\"change_sumon_token\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"emergencyWithdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"flip\",\"type\":\"uint256\"}],\"name\":\"flipValues\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"getApproved\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"giveaway_goats\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"initiatePreSale\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"initiateSale\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"isApprovedForAll\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_address\",\"type\":\"address\"}],\"name\":\"isWalletInPreSale\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"maxGoatMint\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"ownerOf\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"partner1\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"partner10\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"partner11\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"partner12\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"partner13\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"partner2\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"partner3\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"partner4\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"partner5\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"partner6\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"partner7\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"partner8\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"partner9\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"preSaleWalletAddresses\",\"type\":\"address[]\"}],\"name\":\"populate_PreSaleWhitelist\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"presaleSupplyL\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"presaleWhitelistActive\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"price\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"safeTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"_data\",\"type\":\"bytes\"}],\"name\":\"safeTransferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"approved\",\"type\":\"bool\"}],\"name\":\"setApprovalForAll\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"_baseURI\",\"type\":\"string\"}],\"name\":\"set_uri\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newCost\",\"type\":\"uint256\"}],\"name\":\"setsumonCost\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newsumonSupply\",\"type\":\"uint256\"}],\"name\":\"setsumonSupply\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"splitAddy\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256[]\",\"name\":\"token_ids\",\"type\":\"uint256[]\"}],\"name\":\"sumon\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"sumonActive\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_supplyLimit\",\"type\":\"uint256\"}],\"name\":\"sumonAmount\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"sumonCost\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"sumonSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_mintLimit\",\"type\":\"uint256\"}],\"name\":\"sumon_maxGoatMint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_mintPrice\",\"type\":\"uint256\"}],\"name\":\"sumon_mintPrice\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"sumon_token_id\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"supply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"supplyL\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"token_id\",\"type\":\"uint256\"}],\"name\":\"tokenURI\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenId\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"withdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"withdrawES\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}]"

// ERC721 is an auto generated Go binding around an Ethereum contract.
type ERC721 struct {
	ERC721Caller     // Read-only binding to the contract
	ERC721Transactor // Write-only binding to the contract
	ERC721Filterer   // Log filterer for contract events
}

// ERC721Caller is an auto generated read-only Go binding around an Ethereum contract.
type ERC721Caller struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// ERC721Transactor is an auto generated write-only Go binding around an Ethereum contract.
type ERC721Transactor struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// ERC721Filterer is an auto generated log filtering Go binding around an Ethereum contract events.
type ERC721Filterer struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// ERC721Session is an auto generated Go binding around an Ethereum contract,
// with pre-set call and transact options.
type ERC721Session struct {
	Contract     *ERC721           // Generic contract binding to set the session for
	CallOpts     bind.CallOpts     // Call options to use throughout this session
	TransactOpts bind.TransactOpts // Transaction auth options to use throughout this session
}

// ERC721CallerSession is an auto generated read-only Go binding around an Ethereum contract,
// with pre-set call options.
type ERC721CallerSession struct {
	Contract *ERC721Caller // Generic contract caller binding to set the session for
	CallOpts bind.CallOpts // Call options to use throughout this session
}

// ERC721TransactorSession is an auto generated write-only Go binding around an Ethereum contract,
// with pre-set transact options.
type ERC721TransactorSession struct {
	Contract     *ERC721Transactor // Generic contract transactor binding to set the session for
	TransactOpts bind.TransactOpts // Transaction auth options to use throughout this session
}

// ERC721Raw is an auto generated low-level Go binding around an Ethereum contract.
type ERC721Raw struct {
	Contract *ERC721 // Generic contract binding to access the raw methods on
}

// ERC721CallerRaw is an auto generated low-level read-only Go binding around an Ethereum contract.
type ERC721CallerRaw struct {
	Contract *ERC721Caller // Generic read-only contract binding to access the raw methods on
}

// ERC721TransactorRaw is an auto generated low-level write-only Go binding around an Ethereum contract.
type ERC721TransactorRaw struct {
	Contract *ERC721Transactor // Generic write-only contract binding to access the raw methods on
}

// NewERC721 creates a new instance of ERC721, bound to a specific deployed contract.
func NewERC721(address common.Address, backend bind.ContractBackend) (*ERC721, error) {
	contract, err := bindERC721(address, backend, backend, backend)
	if err != nil {
		return nil, err
	}
	return &ERC721{ERC721Caller: ERC721Caller{contract: contract}, ERC721Transactor: ERC721Transactor{contract: contract}, ERC721Filterer: ERC721Filterer{contract: contract}}, nil
}

// NewERC721Caller creates a new read-only instance of ERC721, bound to a specific deployed contract.
func NewERC721Caller(address common.Address, caller bind.ContractCaller) (*ERC721Caller, error) {
	contract, err := bindERC721(address, caller, nil, nil)
	if err != nil {
		return nil, err
	}
	return &ERC721Caller{contract: contract}, nil
}

// NewERC721Transactor creates a new write-only instance of ERC721, bound to a specific deployed contract.
func NewERC721Transactor(address common.Address, transactor bind.ContractTransactor) (*ERC721Transactor, error) {
	contract, err := bindERC721(address, nil, transactor, nil)
	if err != nil {
		return nil, err
	}
	return &ERC721Transactor{contract: contract}, nil
}

// NewERC721Filterer creates a new log filterer instance of ERC721, bound to a specific deployed contract.
func NewERC721Filterer(address common.Address, filterer bind.ContractFilterer) (*ERC721Filterer, error) {
	contract, err := bindERC721(address, nil, nil, filterer)
	if err != nil {
		return nil, err
	}
	return &ERC721Filterer{contract: contract}, nil
}

// bindERC721 binds a generic wrapper to an already deployed contract.
func bindERC721(address common.Address, caller bind.ContractCaller, transactor bind.ContractTransactor, filterer bind.ContractFilterer) (*bind.BoundContract, error) {
	parsed, err := abi.JSON(strings.NewReader(ERC721ABI))
	if err != nil {
		return nil, err
	}
	return bind.NewBoundContract(address, parsed, caller, transactor, filterer), nil
}

// Call invokes the (constant) contract method with params as input values and
// sets the output to result. The result type might be a single field for simple
// returns, a slice of interfaces for anonymous returns and a struct for named
// returns.
func (_ERC721 *ERC721Raw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _ERC721.Contract.ERC721Caller.contract.Call(opts, result, method, params...)
}

// Transfer initiates a plain transaction to move funds to the contract, calling
// its default method if one is available.
func (_ERC721 *ERC721Raw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _ERC721.Contract.ERC721Transactor.contract.Transfer(opts)
}

// Transact invokes the (paid) contract method with params as input values.
func (_ERC721 *ERC721Raw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _ERC721.Contract.ERC721Transactor.contract.Transact(opts, method, params...)
}

// Call invokes the (constant) contract method with params as input values and
// sets the output to result. The result type might be a single field for simple
// returns, a slice of interfaces for anonymous returns and a struct for named
// returns.
func (_ERC721 *ERC721CallerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _ERC721.Contract.contract.Call(opts, result, method, params...)
}

// Transfer initiates a plain transaction to move funds to the contract, calling
// its default method if one is available.
func (_ERC721 *ERC721TransactorRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _ERC721.Contract.contract.Transfer(opts)
}

// Transact invokes the (paid) contract method with params as input values.
func (_ERC721 *ERC721TransactorRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _ERC721.Contract.contract.Transact(opts, method, params...)
}

// GoatPresale is a free data retrieval call binding the contract method 0x0f8847f1.
//
// Solidity: function GoatPresale(uint256 ) view returns(address)
func (_ERC721 *ERC721Caller) GoatPresale(opts *bind.CallOpts, arg0 *big.Int) (common.Address, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "GoatPresale", arg0)

	if err != nil {
		return *new(common.Address), err
	}
	if len(out) == 0 {
		return *new(common.Address), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// GoatPresale is a free data retrieval call binding the contract method 0x0f8847f1.
//
// Solidity: function GoatPresale(uint256 ) view returns(address)
func (_ERC721 *ERC721Session) GoatPresale(arg0 *big.Int) (common.Address, error) {
	return _ERC721.Contract.GoatPresale(&_ERC721.CallOpts, arg0)
}

// GoatPresale is a free data retrieval call binding the contract method 0x0f8847f1.
//
// Solidity: function GoatPresale(uint256 ) view returns(address)
func (_ERC721 *ERC721CallerSession) GoatPresale(arg0 *big.Int) (common.Address, error) {
	return _ERC721.Contract.GoatPresale(&_ERC721.CallOpts, arg0)
}

// BalanceOf is a free data retrieval call binding the contract method 0x70a08231.
//
// Solidity: function balanceOf(address owner) view returns(uint256)
func (_ERC721 *ERC721Caller) BalanceOf(opts *bind.CallOpts, owner common.Address) (*big.Int, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "balanceOf", owner)

	if err != nil {
		return *new(*big.Int), err
	}
	if len(out) == 0 {
		return *new(*big.Int), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// BalanceOf is a free data retrieval call binding the contract method 0x70a08231.
//
// Solidity: function balanceOf(address owner) view returns(uint256)
func (_ERC721 *ERC721Session) BalanceOf(owner common.Address) (*big.Int, error) {
	return _ERC721.Contract.BalanceOf(&_ERC721.CallOpts, owner)
}

// BalanceOf is a free data retrieval call binding the contract method 0x70a08231.
//
// Solidity: function balanceOf(address owner) view returns(uint256)
func (_ERC721 *ERC721CallerSession) BalanceOf(owner common.Address) (*big.Int, error) {
	return _ERC721.Contract.BalanceOf(&_ERC721.CallOpts, owner)
}

// BaseURI is a free data retrieval call binding the contract method 0x6c0360eb.
//
// Solidity: function baseURI() view returns(string)
func (_ERC721 *ERC721Caller) BaseURI(opts *bind.CallOpts) (string, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "baseURI")

	if err != nil {
		return *new(string), err
	}
	if len(out) == 0 {
		return *new(string), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(string)).(*string)

	return out0, err

}

// BaseURI is a free data retrieval call binding the contract method 0x6c0360eb.
//
// Solidity: function baseURI() view returns(string)
func (_ERC721 *ERC721Session) BaseURI() (string, error) {
	return _ERC721.Contract.BaseURI(&_ERC721.CallOpts)
}

// BaseURI is a free data retrieval call binding the contract method 0x6c0360eb.
//
// Solidity: function baseURI() view returns(string)
func (_ERC721 *ERC721CallerSession) BaseURI() (string, error) {
	return _ERC721.Contract.BaseURI(&_ERC721.CallOpts)
}

// CalledWithdrawES is a free data retrieval call binding the contract method 0x2b2d7626.
//
// Solidity: function calledWithdrawES() view returns(bool)
func (_ERC721 *ERC721Caller) CalledWithdrawES(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "calledWithdrawES")

	if err != nil {
		return *new(bool), err
	}
	if len(out) == 0 {
		return *new(bool), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

// CalledWithdrawES is a free data retrieval call binding the contract method 0x2b2d7626.
//
// Solidity: function calledWithdrawES() view returns(bool)
func (_ERC721 *ERC721Session) CalledWithdrawES() (bool, error) {
	return _ERC721.Contract.CalledWithdrawES(&_ERC721.CallOpts)
}

// CalledWithdrawES is a free data retrieval call binding the contract method 0x2b2d7626.
//
// Solidity: function calledWithdrawES() view returns(bool)
func (_ERC721 *ERC721CallerSession) CalledWithdrawES() (bool, error) {
	return _ERC721.Contract.CalledWithdrawES(&_ERC721.CallOpts)
}

// GetApproved is a free data retrieval call binding the contract method 0x081812fc.
//
// Solidity: function getApproved(uint256 tokenId) view returns(address)
func (_ERC721 *ERC721Caller) GetApproved(opts *bind.CallOpts, tokenId *big.Int) (common.Address, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "getApproved", tokenId)

	if err != nil {
		return *new(common.Address), err
	}
	if len(out) == 0 {
		return *new(common.Address), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// GetApproved is a free data retrieval call binding the contract method 0x081812fc.
//
// Solidity: function getApproved(uint256 tokenId) view returns(address)
func (_ERC721 *ERC721Session) GetApproved(tokenId *big.Int) (common.Address, error) {
	return _ERC721.Contract.GetApproved(&_ERC721.CallOpts, tokenId)
}

// GetApproved is a free data retrieval call binding the contract method 0x081812fc.
//
// Solidity: function getApproved(uint256 tokenId) view returns(address)
func (_ERC721 *ERC721CallerSession) GetApproved(tokenId *big.Int) (common.Address, error) {
	return _ERC721.Contract.GetApproved(&_ERC721.CallOpts, tokenId)
}

// InitiatePreSale is a free data retrieval call binding the contract method 0xe449f7c3.
//
// Solidity: function initiatePreSale() view returns(bool)
func (_ERC721 *ERC721Caller) InitiatePreSale(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "initiatePreSale")

	if err != nil {
		return *new(bool), err
	}
	if len(out) == 0 {
		return *new(bool), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

// InitiatePreSale is a free data retrieval call binding the contract method 0xe449f7c3.
//
// Solidity: function initiatePreSale() view returns(bool)
func (_ERC721 *ERC721Session) InitiatePreSale() (bool, error) {
	return _ERC721.Contract.InitiatePreSale(&_ERC721.CallOpts)
}

// InitiatePreSale is a free data retrieval call binding the contract method 0xe449f7c3.
//
// Solidity: function initiatePreSale() view returns(bool)
func (_ERC721 *ERC721CallerSession) InitiatePreSale() (bool, error) {
	return _ERC721.Contract.InitiatePreSale(&_ERC721.CallOpts)
}

// InitiateSale is a free data retrieval call binding the contract method 0xb789e671.
//
// Solidity: function initiateSale() view returns(bool)
func (_ERC721 *ERC721Caller) InitiateSale(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "initiateSale")

	if err != nil {
		return *new(bool), err
	}
	if len(out) == 0 {
		return *new(bool), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

// InitiateSale is a free data retrieval call binding the contract method 0xb789e671.
//
// Solidity: function initiateSale() view returns(bool)
func (_ERC721 *ERC721Session) InitiateSale() (bool, error) {
	return _ERC721.Contract.InitiateSale(&_ERC721.CallOpts)
}

// InitiateSale is a free data retrieval call binding the contract method 0xb789e671.
//
// Solidity: function initiateSale() view returns(bool)
func (_ERC721 *ERC721CallerSession) InitiateSale() (bool, error) {
	return _ERC721.Contract.InitiateSale(&_ERC721.CallOpts)
}

// IsApprovedForAll is a free data retrieval call binding the contract method 0xe985e9c5.
//
// Solidity: function isApprovedForAll(address owner, address operator) view returns(bool)
func (_ERC721 *ERC721Caller) IsApprovedForAll(opts *bind.CallOpts, owner common.Address, operator common.Address) (bool, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "isApprovedForAll", owner, operator)

	if err != nil {
		return *new(bool), err
	}
	if len(out) == 0 {
		return *new(bool), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

// IsApprovedForAll is a free data retrieval call binding the contract method 0xe985e9c5.
//
// Solidity: function isApprovedForAll(address owner, address operator) view returns(bool)
func (_ERC721 *ERC721Session) IsApprovedForAll(owner common.Address, operator common.Address) (bool, error) {
	return _ERC721.Contract.IsApprovedForAll(&_ERC721.CallOpts, owner, operator)
}

// IsApprovedForAll is a free data retrieval call binding the contract method 0xe985e9c5.
//
// Solidity: function isApprovedForAll(address owner, address operator) view returns(bool)
func (_ERC721 *ERC721CallerSession) IsApprovedForAll(owner common.Address, operator common.Address) (bool, error) {
	return _ERC721.Contract.IsApprovedForAll(&_ERC721.CallOpts, owner, operator)
}

// IsWalletInPreSale is a free data retrieval call binding the contract method 0xae8e5ec9.
//
// Solidity: function isWalletInPreSale(address _address) view returns(bool)
func (_ERC721 *ERC721Caller) IsWalletInPreSale(opts *bind.CallOpts, _address common.Address) (bool, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "isWalletInPreSale", _address)

	if err != nil {
		return *new(bool), err
	}
	if len(out) == 0 {
		return *new(bool), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

// IsWalletInPreSale is a free data retrieval call binding the contract method 0xae8e5ec9.
//
// Solidity: function isWalletInPreSale(address _address) view returns(bool)
func (_ERC721 *ERC721Session) IsWalletInPreSale(_address common.Address) (bool, error) {
	return _ERC721.Contract.IsWalletInPreSale(&_ERC721.CallOpts, _address)
}

// IsWalletInPreSale is a free data retrieval call binding the contract method 0xae8e5ec9.
//
// Solidity: function isWalletInPreSale(address _address) view returns(bool)
func (_ERC721 *ERC721CallerSession) IsWalletInPreSale(_address common.Address) (bool, error) {
	return _ERC721.Contract.IsWalletInPreSale(&_ERC721.CallOpts, _address)
}

// MaxGoatMint is a free data retrieval call binding the contract method 0x84a299c3.
//
// Solidity: function maxGoatMint() view returns(uint256)
func (_ERC721 *ERC721Caller) MaxGoatMint(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "maxGoatMint")

	if err != nil {
		return *new(*big.Int), err
	}
	if len(out) == 0 {
		return *new(*big.Int), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// MaxGoatMint is a free data retrieval call binding the contract method 0x84a299c3.
//
// Solidity: function maxGoatMint() view returns(uint256)
func (_ERC721 *ERC721Session) MaxGoatMint() (*big.Int, error) {
	return _ERC721.Contract.MaxGoatMint(&_ERC721.CallOpts)
}

// MaxGoatMint is a free data retrieval call binding the contract method 0x84a299c3.
//
// Solidity: function maxGoatMint() view returns(uint256)
func (_ERC721 *ERC721CallerSession) MaxGoatMint() (*big.Int, error) {
	return _ERC721.Contract.MaxGoatMint(&_ERC721.CallOpts)
}

// Name is a free data retrieval call binding the contract method 0x06fdde03.
//
// Solidity: function name() view returns(string)
func (_ERC721 *ERC721Caller) Name(opts *bind.CallOpts) (string, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "name")

	if err != nil {
		return *new(string), err
	}
	if len(out) == 0 {
		return *new(string), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(string)).(*string)

	return out0, err

}

// Name is a free data retrieval call binding the contract method 0x06fdde03.
//
// Solidity: function name() view returns(string)
func (_ERC721 *ERC721Session) Name() (string, error) {
	return _ERC721.Contract.Name(&_ERC721.CallOpts)
}

// Name is a free data retrieval call binding the contract method 0x06fdde03.
//
// Solidity: function name() view returns(string)
func (_ERC721 *ERC721CallerSession) Name() (string, error) {
	return _ERC721.Contract.Name(&_ERC721.CallOpts)
}

// Owner is a free data retrieval call binding the contract method 0x8da5cb5b.
//
// Solidity: function owner() view returns(address)
func (_ERC721 *ERC721Caller) Owner(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "owner")

	if err != nil {
		return *new(common.Address), err
	}
	if len(out) == 0 {
		return *new(common.Address), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// Owner is a free data retrieval call binding the contract method 0x8da5cb5b.
//
// Solidity: function owner() view returns(address)
func (_ERC721 *ERC721Session) Owner() (common.Address, error) {
	return _ERC721.Contract.Owner(&_ERC721.CallOpts)
}

// Owner is a free data retrieval call binding the contract method 0x8da5cb5b.
//
// Solidity: function owner() view returns(address)
func (_ERC721 *ERC721CallerSession) Owner() (common.Address, error) {
	return _ERC721.Contract.Owner(&_ERC721.CallOpts)
}

// OwnerOf is a free data retrieval call binding the contract method 0x6352211e.
//
// Solidity: function ownerOf(uint256 tokenId) view returns(address)
func (_ERC721 *ERC721Caller) OwnerOf(opts *bind.CallOpts, tokenId *big.Int) (common.Address, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "ownerOf", tokenId)

	if err != nil {
		return *new(common.Address), err
	}
	if len(out) == 0 {
		return *new(common.Address), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// OwnerOf is a free data retrieval call binding the contract method 0x6352211e.
//
// Solidity: function ownerOf(uint256 tokenId) view returns(address)
func (_ERC721 *ERC721Session) OwnerOf(tokenId *big.Int) (common.Address, error) {
	return _ERC721.Contract.OwnerOf(&_ERC721.CallOpts, tokenId)
}

// OwnerOf is a free data retrieval call binding the contract method 0x6352211e.
//
// Solidity: function ownerOf(uint256 tokenId) view returns(address)
func (_ERC721 *ERC721CallerSession) OwnerOf(tokenId *big.Int) (common.Address, error) {
	return _ERC721.Contract.OwnerOf(&_ERC721.CallOpts, tokenId)
}

// Partner1 is a free data retrieval call binding the contract method 0xa4c7c7b3.
//
// Solidity: function partner1() view returns(address)
func (_ERC721 *ERC721Caller) Partner1(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "partner1")

	if err != nil {
		return *new(common.Address), err
	}
	if len(out) == 0 {
		return *new(common.Address), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// Partner1 is a free data retrieval call binding the contract method 0xa4c7c7b3.
//
// Solidity: function partner1() view returns(address)
func (_ERC721 *ERC721Session) Partner1() (common.Address, error) {
	return _ERC721.Contract.Partner1(&_ERC721.CallOpts)
}

// Partner1 is a free data retrieval call binding the contract method 0xa4c7c7b3.
//
// Solidity: function partner1() view returns(address)
func (_ERC721 *ERC721CallerSession) Partner1() (common.Address, error) {
	return _ERC721.Contract.Partner1(&_ERC721.CallOpts)
}

// Partner10 is a free data retrieval call binding the contract method 0x91f58daa.
//
// Solidity: function partner10() view returns(address)
func (_ERC721 *ERC721Caller) Partner10(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "partner10")

	if err != nil {
		return *new(common.Address), err
	}
	if len(out) == 0 {
		return *new(common.Address), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// Partner10 is a free data retrieval call binding the contract method 0x91f58daa.
//
// Solidity: function partner10() view returns(address)
func (_ERC721 *ERC721Session) Partner10() (common.Address, error) {
	return _ERC721.Contract.Partner10(&_ERC721.CallOpts)
}

// Partner10 is a free data retrieval call binding the contract method 0x91f58daa.
//
// Solidity: function partner10() view returns(address)
func (_ERC721 *ERC721CallerSession) Partner10() (common.Address, error) {
	return _ERC721.Contract.Partner10(&_ERC721.CallOpts)
}

// Partner11 is a free data retrieval call binding the contract method 0x6dab4c69.
//
// Solidity: function partner11() view returns(address)
func (_ERC721 *ERC721Caller) Partner11(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "partner11")

	if err != nil {
		return *new(common.Address), err
	}
	if len(out) == 0 {
		return *new(common.Address), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// Partner11 is a free data retrieval call binding the contract method 0x6dab4c69.
//
// Solidity: function partner11() view returns(address)
func (_ERC721 *ERC721Session) Partner11() (common.Address, error) {
	return _ERC721.Contract.Partner11(&_ERC721.CallOpts)
}

// Partner11 is a free data retrieval call binding the contract method 0x6dab4c69.
//
// Solidity: function partner11() view returns(address)
func (_ERC721 *ERC721CallerSession) Partner11() (common.Address, error) {
	return _ERC721.Contract.Partner11(&_ERC721.CallOpts)
}

// Partner12 is a free data retrieval call binding the contract method 0xfcd8d151.
//
// Solidity: function partner12() view returns(address)
func (_ERC721 *ERC721Caller) Partner12(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "partner12")

	if err != nil {
		return *new(common.Address), err
	}
	if len(out) == 0 {
		return *new(common.Address), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// Partner12 is a free data retrieval call binding the contract method 0xfcd8d151.
//
// Solidity: function partner12() view returns(address)
func (_ERC721 *ERC721Session) Partner12() (common.Address, error) {
	return _ERC721.Contract.Partner12(&_ERC721.CallOpts)
}

// Partner12 is a free data retrieval call binding the contract method 0xfcd8d151.
//
// Solidity: function partner12() view returns(address)
func (_ERC721 *ERC721CallerSession) Partner12() (common.Address, error) {
	return _ERC721.Contract.Partner12(&_ERC721.CallOpts)
}

// Partner13 is a free data retrieval call binding the contract method 0x14703a47.
//
// Solidity: function partner13() view returns(address)
func (_ERC721 *ERC721Caller) Partner13(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "partner13")

	if err != nil {
		return *new(common.Address), err
	}
	if len(out) == 0 {
		return *new(common.Address), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// Partner13 is a free data retrieval call binding the contract method 0x14703a47.
//
// Solidity: function partner13() view returns(address)
func (_ERC721 *ERC721Session) Partner13() (common.Address, error) {
	return _ERC721.Contract.Partner13(&_ERC721.CallOpts)
}

// Partner13 is a free data retrieval call binding the contract method 0x14703a47.
//
// Solidity: function partner13() view returns(address)
func (_ERC721 *ERC721CallerSession) Partner13() (common.Address, error) {
	return _ERC721.Contract.Partner13(&_ERC721.CallOpts)
}

// Partner2 is a free data retrieval call binding the contract method 0x8b35a244.
//
// Solidity: function partner2() view returns(address)
func (_ERC721 *ERC721Caller) Partner2(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "partner2")

	if err != nil {
		return *new(common.Address), err
	}
	if len(out) == 0 {
		return *new(common.Address), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// Partner2 is a free data retrieval call binding the contract method 0x8b35a244.
//
// Solidity: function partner2() view returns(address)
func (_ERC721 *ERC721Session) Partner2() (common.Address, error) {
	return _ERC721.Contract.Partner2(&_ERC721.CallOpts)
}

// Partner2 is a free data retrieval call binding the contract method 0x8b35a244.
//
// Solidity: function partner2() view returns(address)
func (_ERC721 *ERC721CallerSession) Partner2() (common.Address, error) {
	return _ERC721.Contract.Partner2(&_ERC721.CallOpts)
}

// Partner3 is a free data retrieval call binding the contract method 0x89eb727c.
//
// Solidity: function partner3() view returns(address)
func (_ERC721 *ERC721Caller) Partner3(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "partner3")

	if err != nil {
		return *new(common.Address), err
	}
	if len(out) == 0 {
		return *new(common.Address), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// Partner3 is a free data retrieval call binding the contract method 0x89eb727c.
//
// Solidity: function partner3() view returns(address)
func (_ERC721 *ERC721Session) Partner3() (common.Address, error) {
	return _ERC721.Contract.Partner3(&_ERC721.CallOpts)
}

// Partner3 is a free data retrieval call binding the contract method 0x89eb727c.
//
// Solidity: function partner3() view returns(address)
func (_ERC721 *ERC721CallerSession) Partner3() (common.Address, error) {
	return _ERC721.Contract.Partner3(&_ERC721.CallOpts)
}

// Partner4 is a free data retrieval call binding the contract method 0xcfd5e8ac.
//
// Solidity: function partner4() view returns(address)
func (_ERC721 *ERC721Caller) Partner4(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "partner4")

	if err != nil {
		return *new(common.Address), err
	}
	if len(out) == 0 {
		return *new(common.Address), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// Partner4 is a free data retrieval call binding the contract method 0xcfd5e8ac.
//
// Solidity: function partner4() view returns(address)
func (_ERC721 *ERC721Session) Partner4() (common.Address, error) {
	return _ERC721.Contract.Partner4(&_ERC721.CallOpts)
}

// Partner4 is a free data retrieval call binding the contract method 0xcfd5e8ac.
//
// Solidity: function partner4() view returns(address)
func (_ERC721 *ERC721CallerSession) Partner4() (common.Address, error) {
	return _ERC721.Contract.Partner4(&_ERC721.CallOpts)
}

// Partner5 is a free data retrieval call binding the contract method 0xe3d48880.
//
// Solidity: function partner5() view returns(address)
func (_ERC721 *ERC721Caller) Partner5(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "partner5")

	if err != nil {
		return *new(common.Address), err
	}
	if len(out) == 0 {
		return *new(common.Address), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// Partner5 is a free data retrieval call binding the contract method 0xe3d48880.
//
// Solidity: function partner5() view returns(address)
func (_ERC721 *ERC721Session) Partner5() (common.Address, error) {
	return _ERC721.Contract.Partner5(&_ERC721.CallOpts)
}

// Partner5 is a free data retrieval call binding the contract method 0xe3d48880.
//
// Solidity: function partner5() view returns(address)
func (_ERC721 *ERC721CallerSession) Partner5() (common.Address, error) {
	return _ERC721.Contract.Partner5(&_ERC721.CallOpts)
}

// Partner6 is a free data retrieval call binding the contract method 0x6f1527e8.
//
// Solidity: function partner6() view returns(address)
func (_ERC721 *ERC721Caller) Partner6(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "partner6")

	if err != nil {
		return *new(common.Address), err
	}
	if len(out) == 0 {
		return *new(common.Address), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// Partner6 is a free data retrieval call binding the contract method 0x6f1527e8.
//
// Solidity: function partner6() view returns(address)
func (_ERC721 *ERC721Session) Partner6() (common.Address, error) {
	return _ERC721.Contract.Partner6(&_ERC721.CallOpts)
}

// Partner6 is a free data retrieval call binding the contract method 0x6f1527e8.
//
// Solidity: function partner6() view returns(address)
func (_ERC721 *ERC721CallerSession) Partner6() (common.Address, error) {
	return _ERC721.Contract.Partner6(&_ERC721.CallOpts)
}

// Partner7 is a free data retrieval call binding the contract method 0x0f37f762.
//
// Solidity: function partner7() view returns(address)
func (_ERC721 *ERC721Caller) Partner7(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "partner7")

	if err != nil {
		return *new(common.Address), err
	}
	if len(out) == 0 {
		return *new(common.Address), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// Partner7 is a free data retrieval call binding the contract method 0x0f37f762.
//
// Solidity: function partner7() view returns(address)
func (_ERC721 *ERC721Session) Partner7() (common.Address, error) {
	return _ERC721.Contract.Partner7(&_ERC721.CallOpts)
}

// Partner7 is a free data retrieval call binding the contract method 0x0f37f762.
//
// Solidity: function partner7() view returns(address)
func (_ERC721 *ERC721CallerSession) Partner7() (common.Address, error) {
	return _ERC721.Contract.Partner7(&_ERC721.CallOpts)
}

// Partner8 is a free data retrieval call binding the contract method 0x133a8f6f.
//
// Solidity: function partner8() view returns(address)
func (_ERC721 *ERC721Caller) Partner8(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "partner8")

	if err != nil {
		return *new(common.Address), err
	}
	if len(out) == 0 {
		return *new(common.Address), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// Partner8 is a free data retrieval call binding the contract method 0x133a8f6f.
//
// Solidity: function partner8() view returns(address)
func (_ERC721 *ERC721Session) Partner8() (common.Address, error) {
	return _ERC721.Contract.Partner8(&_ERC721.CallOpts)
}

// Partner8 is a free data retrieval call binding the contract method 0x133a8f6f.
//
// Solidity: function partner8() view returns(address)
func (_ERC721 *ERC721CallerSession) Partner8() (common.Address, error) {
	return _ERC721.Contract.Partner8(&_ERC721.CallOpts)
}

// Partner9 is a free data retrieval call binding the contract method 0xe7e8214c.
//
// Solidity: function partner9() view returns(address)
func (_ERC721 *ERC721Caller) Partner9(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "partner9")

	if err != nil {
		return *new(common.Address), err
	}
	if len(out) == 0 {
		return *new(common.Address), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// Partner9 is a free data retrieval call binding the contract method 0xe7e8214c.
//
// Solidity: function partner9() view returns(address)
func (_ERC721 *ERC721Session) Partner9() (common.Address, error) {
	return _ERC721.Contract.Partner9(&_ERC721.CallOpts)
}

// Partner9 is a free data retrieval call binding the contract method 0xe7e8214c.
//
// Solidity: function partner9() view returns(address)
func (_ERC721 *ERC721CallerSession) Partner9() (common.Address, error) {
	return _ERC721.Contract.Partner9(&_ERC721.CallOpts)
}

// PresaleSupplyL is a free data retrieval call binding the contract method 0xb14b2664.
//
// Solidity: function presaleSupplyL() view returns(uint256)
func (_ERC721 *ERC721Caller) PresaleSupplyL(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "presaleSupplyL")

	if err != nil {
		return *new(*big.Int), err
	}
	if len(out) == 0 {
		return *new(*big.Int), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// PresaleSupplyL is a free data retrieval call binding the contract method 0xb14b2664.
//
// Solidity: function presaleSupplyL() view returns(uint256)
func (_ERC721 *ERC721Session) PresaleSupplyL() (*big.Int, error) {
	return _ERC721.Contract.PresaleSupplyL(&_ERC721.CallOpts)
}

// PresaleSupplyL is a free data retrieval call binding the contract method 0xb14b2664.
//
// Solidity: function presaleSupplyL() view returns(uint256)
func (_ERC721 *ERC721CallerSession) PresaleSupplyL() (*big.Int, error) {
	return _ERC721.Contract.PresaleSupplyL(&_ERC721.CallOpts)
}

// PresaleWhitelistActive is a free data retrieval call binding the contract method 0x5f4ce48a.
//
// Solidity: function presaleWhitelistActive() view returns(bool)
func (_ERC721 *ERC721Caller) PresaleWhitelistActive(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "presaleWhitelistActive")

	if err != nil {
		return *new(bool), err
	}
	if len(out) == 0 {
		return *new(bool), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

// PresaleWhitelistActive is a free data retrieval call binding the contract method 0x5f4ce48a.
//
// Solidity: function presaleWhitelistActive() view returns(bool)
func (_ERC721 *ERC721Session) PresaleWhitelistActive() (bool, error) {
	return _ERC721.Contract.PresaleWhitelistActive(&_ERC721.CallOpts)
}

// PresaleWhitelistActive is a free data retrieval call binding the contract method 0x5f4ce48a.
//
// Solidity: function presaleWhitelistActive() view returns(bool)
func (_ERC721 *ERC721CallerSession) PresaleWhitelistActive() (bool, error) {
	return _ERC721.Contract.PresaleWhitelistActive(&_ERC721.CallOpts)
}

// Price is a free data retrieval call binding the contract method 0xa035b1fe.
//
// Solidity: function price() view returns(uint256)
func (_ERC721 *ERC721Caller) Price(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "price")

	if err != nil {
		return *new(*big.Int), err
	}
	if len(out) == 0 {
		return *new(*big.Int), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// Price is a free data retrieval call binding the contract method 0xa035b1fe.
//
// Solidity: function price() view returns(uint256)
func (_ERC721 *ERC721Session) Price() (*big.Int, error) {
	return _ERC721.Contract.Price(&_ERC721.CallOpts)
}

// Price is a free data retrieval call binding the contract method 0xa035b1fe.
//
// Solidity: function price() view returns(uint256)
func (_ERC721 *ERC721CallerSession) Price() (*big.Int, error) {
	return _ERC721.Contract.Price(&_ERC721.CallOpts)
}

// SplitAddy is a free data retrieval call binding the contract method 0x8a41048c.
//
// Solidity: function splitAddy() view returns(address)
func (_ERC721 *ERC721Caller) SplitAddy(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "splitAddy")

	if err != nil {
		return *new(common.Address), err
	}
	if len(out) == 0 {
		return *new(common.Address), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// SplitAddy is a free data retrieval call binding the contract method 0x8a41048c.
//
// Solidity: function splitAddy() view returns(address)
func (_ERC721 *ERC721Session) SplitAddy() (common.Address, error) {
	return _ERC721.Contract.SplitAddy(&_ERC721.CallOpts)
}

// SplitAddy is a free data retrieval call binding the contract method 0x8a41048c.
//
// Solidity: function splitAddy() view returns(address)
func (_ERC721 *ERC721CallerSession) SplitAddy() (common.Address, error) {
	return _ERC721.Contract.SplitAddy(&_ERC721.CallOpts)
}

// SumonActive is a free data retrieval call binding the contract method 0xba47861c.
//
// Solidity: function sumonActive() view returns(bool)
func (_ERC721 *ERC721Caller) SumonActive(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "sumonActive")

	if err != nil {
		return *new(bool), err
	}
	if len(out) == 0 {
		return *new(bool), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

// SumonActive is a free data retrieval call binding the contract method 0xba47861c.
//
// Solidity: function sumonActive() view returns(bool)
func (_ERC721 *ERC721Session) SumonActive() (bool, error) {
	return _ERC721.Contract.SumonActive(&_ERC721.CallOpts)
}

// SumonActive is a free data retrieval call binding the contract method 0xba47861c.
//
// Solidity: function sumonActive() view returns(bool)
func (_ERC721 *ERC721CallerSession) SumonActive() (bool, error) {
	return _ERC721.Contract.SumonActive(&_ERC721.CallOpts)
}

// SumonCost is a free data retrieval call binding the contract method 0x462194e9.
//
// Solidity: function sumonCost() view returns(uint256)
func (_ERC721 *ERC721Caller) SumonCost(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "sumonCost")

	if err != nil {
		return *new(*big.Int), err
	}
	if len(out) == 0 {
		return *new(*big.Int), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// SumonCost is a free data retrieval call binding the contract method 0x462194e9.
//
// Solidity: function sumonCost() view returns(uint256)
func (_ERC721 *ERC721Session) SumonCost() (*big.Int, error) {
	return _ERC721.Contract.SumonCost(&_ERC721.CallOpts)
}

// SumonCost is a free data retrieval call binding the contract method 0x462194e9.
//
// Solidity: function sumonCost() view returns(uint256)
func (_ERC721 *ERC721CallerSession) SumonCost() (*big.Int, error) {
	return _ERC721.Contract.SumonCost(&_ERC721.CallOpts)
}

// SumonSupply is a free data retrieval call binding the contract method 0xa3ee642f.
//
// Solidity: function sumonSupply() view returns(uint256)
func (_ERC721 *ERC721Caller) SumonSupply(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "sumonSupply")

	if err != nil {
		return *new(*big.Int), err
	}
	if len(out) == 0 {
		return *new(*big.Int), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// SumonSupply is a free data retrieval call binding the contract method 0xa3ee642f.
//
// Solidity: function sumonSupply() view returns(uint256)
func (_ERC721 *ERC721Session) SumonSupply() (*big.Int, error) {
	return _ERC721.Contract.SumonSupply(&_ERC721.CallOpts)
}

// SumonSupply is a free data retrieval call binding the contract method 0xa3ee642f.
//
// Solidity: function sumonSupply() view returns(uint256)
func (_ERC721 *ERC721CallerSession) SumonSupply() (*big.Int, error) {
	return _ERC721.Contract.SumonSupply(&_ERC721.CallOpts)
}

// SumonTokenId is a free data retrieval call binding the contract method 0x98d331af.
//
// Solidity: function sumon_token_id() view returns(uint256)
func (_ERC721 *ERC721Caller) SumonTokenId(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "sumon_token_id")

	if err != nil {
		return *new(*big.Int), err
	}
	if len(out) == 0 {
		return *new(*big.Int), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// SumonTokenId is a free data retrieval call binding the contract method 0x98d331af.
//
// Solidity: function sumon_token_id() view returns(uint256)
func (_ERC721 *ERC721Session) SumonTokenId() (*big.Int, error) {
	return _ERC721.Contract.SumonTokenId(&_ERC721.CallOpts)
}

// SumonTokenId is a free data retrieval call binding the contract method 0x98d331af.
//
// Solidity: function sumon_token_id() view returns(uint256)
func (_ERC721 *ERC721CallerSession) SumonTokenId() (*big.Int, error) {
	return _ERC721.Contract.SumonTokenId(&_ERC721.CallOpts)
}

// Supply is a free data retrieval call binding the contract method 0x047fc9aa.
//
// Solidity: function supply() view returns(uint256)
func (_ERC721 *ERC721Caller) Supply(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "supply")

	if err != nil {
		return *new(*big.Int), err
	}
	if len(out) == 0 {
		return *new(*big.Int), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// Supply is a free data retrieval call binding the contract method 0x047fc9aa.
//
// Solidity: function supply() view returns(uint256)
func (_ERC721 *ERC721Session) Supply() (*big.Int, error) {
	return _ERC721.Contract.Supply(&_ERC721.CallOpts)
}

// Supply is a free data retrieval call binding the contract method 0x047fc9aa.
//
// Solidity: function supply() view returns(uint256)
func (_ERC721 *ERC721CallerSession) Supply() (*big.Int, error) {
	return _ERC721.Contract.Supply(&_ERC721.CallOpts)
}

// SupplyL is a free data retrieval call binding the contract method 0xfb02df15.
//
// Solidity: function supplyL() view returns(uint256)
func (_ERC721 *ERC721Caller) SupplyL(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "supplyL")

	if err != nil {
		return *new(*big.Int), err
	}
	if len(out) == 0 {
		return *new(*big.Int), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// SupplyL is a free data retrieval call binding the contract method 0xfb02df15.
//
// Solidity: function supplyL() view returns(uint256)
func (_ERC721 *ERC721Session) SupplyL() (*big.Int, error) {
	return _ERC721.Contract.SupplyL(&_ERC721.CallOpts)
}

// SupplyL is a free data retrieval call binding the contract method 0xfb02df15.
//
// Solidity: function supplyL() view returns(uint256)
func (_ERC721 *ERC721CallerSession) SupplyL() (*big.Int, error) {
	return _ERC721.Contract.SupplyL(&_ERC721.CallOpts)
}

// SupportsInterface is a free data retrieval call binding the contract method 0x01ffc9a7.
//
// Solidity: function supportsInterface(bytes4 interfaceId) view returns(bool)
func (_ERC721 *ERC721Caller) SupportsInterface(opts *bind.CallOpts, interfaceId [4]byte) (bool, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "supportsInterface", interfaceId)

	if err != nil {
		return *new(bool), err
	}
	if len(out) == 0 {
		return *new(bool), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

// SupportsInterface is a free data retrieval call binding the contract method 0x01ffc9a7.
//
// Solidity: function supportsInterface(bytes4 interfaceId) view returns(bool)
func (_ERC721 *ERC721Session) SupportsInterface(interfaceId [4]byte) (bool, error) {
	return _ERC721.Contract.SupportsInterface(&_ERC721.CallOpts, interfaceId)
}

// SupportsInterface is a free data retrieval call binding the contract method 0x01ffc9a7.
//
// Solidity: function supportsInterface(bytes4 interfaceId) view returns(bool)
func (_ERC721 *ERC721CallerSession) SupportsInterface(interfaceId [4]byte) (bool, error) {
	return _ERC721.Contract.SupportsInterface(&_ERC721.CallOpts, interfaceId)
}

// Symbol is a free data retrieval call binding the contract method 0x95d89b41.
//
// Solidity: function symbol() view returns(string)
func (_ERC721 *ERC721Caller) Symbol(opts *bind.CallOpts) (string, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "symbol")

	if err != nil {
		return *new(string), err
	}
	if len(out) == 0 {
		return *new(string), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(string)).(*string)

	return out0, err

}

// Symbol is a free data retrieval call binding the contract method 0x95d89b41.
//
// Solidity: function symbol() view returns(string)
func (_ERC721 *ERC721Session) Symbol() (string, error) {
	return _ERC721.Contract.Symbol(&_ERC721.CallOpts)
}

// Symbol is a free data retrieval call binding the contract method 0x95d89b41.
//
// Solidity: function symbol() view returns(string)
func (_ERC721 *ERC721CallerSession) Symbol() (string, error) {
	return _ERC721.Contract.Symbol(&_ERC721.CallOpts)
}

// TokenURI is a free data retrieval call binding the contract method 0xc87b56dd.
//
// Solidity: function tokenURI(uint256 token_id) view returns(string)
func (_ERC721 *ERC721Caller) TokenURI(opts *bind.CallOpts, token_id *big.Int) (string, error) {
	var out []interface{}
	err := _ERC721.contract.Call(opts, &out, "tokenURI", token_id)

	if err != nil {
		return *new(string), err
	}
	if len(out) == 0 {
		return *new(string), errors.New("get nil response")
	}

	out0 := *abi.ConvertType(out[0], new(string)).(*string)

	return out0, err

}

// TokenURI is a free data retrieval call binding the contract method 0xc87b56dd.
//
// Solidity: function tokenURI(uint256 token_id) view returns(string)
func (_ERC721 *ERC721Session) TokenURI(token_id *big.Int) (string, error) {
	return _ERC721.Contract.TokenURI(&_ERC721.CallOpts, token_id)
}

// TokenURI is a free data retrieval call binding the contract method 0xc87b56dd.
//
// Solidity: function tokenURI(uint256 token_id) view returns(string)
func (_ERC721 *ERC721CallerSession) TokenURI(token_id *big.Int) (string, error) {
	return _ERC721.Contract.TokenURI(&_ERC721.CallOpts, token_id)
}

// Approve is a paid mutator transaction binding the contract method 0x095ea7b3.
//
// Solidity: function approve(address to, uint256 tokenId) returns()
func (_ERC721 *ERC721Transactor) Approve(opts *bind.TransactOpts, to common.Address, tokenId *big.Int) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "approve", to, tokenId)
}

// Approve is a paid mutator transaction binding the contract method 0x095ea7b3.
//
// Solidity: function approve(address to, uint256 tokenId) returns()
func (_ERC721 *ERC721Session) Approve(to common.Address, tokenId *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.Approve(&_ERC721.TransactOpts, to, tokenId)
}

// Approve is a paid mutator transaction binding the contract method 0x095ea7b3.
//
// Solidity: function approve(address to, uint256 tokenId) returns()
func (_ERC721 *ERC721TransactorSession) Approve(to common.Address, tokenId *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.Approve(&_ERC721.TransactOpts, to, tokenId)
}

// Buy is a paid mutator transaction binding the contract method 0xd96a094a.
//
// Solidity: function buy(uint256 nft) payable returns()
func (_ERC721 *ERC721Transactor) Buy(opts *bind.TransactOpts, nft *big.Int) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "buy", nft)
}

// Buy is a paid mutator transaction binding the contract method 0xd96a094a.
//
// Solidity: function buy(uint256 nft) payable returns()
func (_ERC721 *ERC721Session) Buy(nft *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.Buy(&_ERC721.TransactOpts, nft)
}

// Buy is a paid mutator transaction binding the contract method 0xd96a094a.
//
// Solidity: function buy(uint256 nft) payable returns()
func (_ERC721 *ERC721TransactorSession) Buy(nft *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.Buy(&_ERC721.TransactOpts, nft)
}

// BuyPresale is a paid mutator transaction binding the contract method 0xbb2841c5.
//
// Solidity: function buy_presale(uint256 nft) payable returns()
func (_ERC721 *ERC721Transactor) BuyPresale(opts *bind.TransactOpts, nft *big.Int) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "buy_presale", nft)
}

// BuyPresale is a paid mutator transaction binding the contract method 0xbb2841c5.
//
// Solidity: function buy_presale(uint256 nft) payable returns()
func (_ERC721 *ERC721Session) BuyPresale(nft *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.BuyPresale(&_ERC721.TransactOpts, nft)
}

// BuyPresale is a paid mutator transaction binding the contract method 0xbb2841c5.
//
// Solidity: function buy_presale(uint256 nft) payable returns()
func (_ERC721 *ERC721TransactorSession) BuyPresale(nft *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.BuyPresale(&_ERC721.TransactOpts, nft)
}

// ChangeSumonToken is a paid mutator transaction binding the contract method 0xaa4981e5.
//
// Solidity: function change_sumon_token(uint256 _sumon_token_id) returns()
func (_ERC721 *ERC721Transactor) ChangeSumonToken(opts *bind.TransactOpts, _sumon_token_id *big.Int) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "change_sumon_token", _sumon_token_id)
}

// ChangeSumonToken is a paid mutator transaction binding the contract method 0xaa4981e5.
//
// Solidity: function change_sumon_token(uint256 _sumon_token_id) returns()
func (_ERC721 *ERC721Session) ChangeSumonToken(_sumon_token_id *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.ChangeSumonToken(&_ERC721.TransactOpts, _sumon_token_id)
}

// ChangeSumonToken is a paid mutator transaction binding the contract method 0xaa4981e5.
//
// Solidity: function change_sumon_token(uint256 _sumon_token_id) returns()
func (_ERC721 *ERC721TransactorSession) ChangeSumonToken(_sumon_token_id *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.ChangeSumonToken(&_ERC721.TransactOpts, _sumon_token_id)
}

// EmergencyWithdraw is a paid mutator transaction binding the contract method 0xdb2e21bc.
//
// Solidity: function emergencyWithdraw() returns()
func (_ERC721 *ERC721Transactor) EmergencyWithdraw(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "emergencyWithdraw")
}

// EmergencyWithdraw is a paid mutator transaction binding the contract method 0xdb2e21bc.
//
// Solidity: function emergencyWithdraw() returns()
func (_ERC721 *ERC721Session) EmergencyWithdraw() (*types.Transaction, error) {
	return _ERC721.Contract.EmergencyWithdraw(&_ERC721.TransactOpts)
}

// EmergencyWithdraw is a paid mutator transaction binding the contract method 0xdb2e21bc.
//
// Solidity: function emergencyWithdraw() returns()
func (_ERC721 *ERC721TransactorSession) EmergencyWithdraw() (*types.Transaction, error) {
	return _ERC721.Contract.EmergencyWithdraw(&_ERC721.TransactOpts)
}

// FlipValues is a paid mutator transaction binding the contract method 0xd676819f.
//
// Solidity: function flipValues(uint256 flip) returns()
func (_ERC721 *ERC721Transactor) FlipValues(opts *bind.TransactOpts, flip *big.Int) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "flipValues", flip)
}

// FlipValues is a paid mutator transaction binding the contract method 0xd676819f.
//
// Solidity: function flipValues(uint256 flip) returns()
func (_ERC721 *ERC721Session) FlipValues(flip *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.FlipValues(&_ERC721.TransactOpts, flip)
}

// FlipValues is a paid mutator transaction binding the contract method 0xd676819f.
//
// Solidity: function flipValues(uint256 flip) returns()
func (_ERC721 *ERC721TransactorSession) FlipValues(flip *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.FlipValues(&_ERC721.TransactOpts, flip)
}

// GiveawayGoats is a paid mutator transaction binding the contract method 0x996a9f97.
//
// Solidity: function giveaway_goats() returns()
func (_ERC721 *ERC721Transactor) GiveawayGoats(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "giveaway_goats")
}

// GiveawayGoats is a paid mutator transaction binding the contract method 0x996a9f97.
//
// Solidity: function giveaway_goats() returns()
func (_ERC721 *ERC721Session) GiveawayGoats() (*types.Transaction, error) {
	return _ERC721.Contract.GiveawayGoats(&_ERC721.TransactOpts)
}

// GiveawayGoats is a paid mutator transaction binding the contract method 0x996a9f97.
//
// Solidity: function giveaway_goats() returns()
func (_ERC721 *ERC721TransactorSession) GiveawayGoats() (*types.Transaction, error) {
	return _ERC721.Contract.GiveawayGoats(&_ERC721.TransactOpts)
}

// PopulatePreSaleWhitelist is a paid mutator transaction binding the contract method 0xe6f17045.
//
// Solidity: function populate_PreSaleWhitelist(address[] preSaleWalletAddresses) returns()
func (_ERC721 *ERC721Transactor) PopulatePreSaleWhitelist(opts *bind.TransactOpts, preSaleWalletAddresses []common.Address) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "populate_PreSaleWhitelist", preSaleWalletAddresses)
}

// PopulatePreSaleWhitelist is a paid mutator transaction binding the contract method 0xe6f17045.
//
// Solidity: function populate_PreSaleWhitelist(address[] preSaleWalletAddresses) returns()
func (_ERC721 *ERC721Session) PopulatePreSaleWhitelist(preSaleWalletAddresses []common.Address) (*types.Transaction, error) {
	return _ERC721.Contract.PopulatePreSaleWhitelist(&_ERC721.TransactOpts, preSaleWalletAddresses)
}

// PopulatePreSaleWhitelist is a paid mutator transaction binding the contract method 0xe6f17045.
//
// Solidity: function populate_PreSaleWhitelist(address[] preSaleWalletAddresses) returns()
func (_ERC721 *ERC721TransactorSession) PopulatePreSaleWhitelist(preSaleWalletAddresses []common.Address) (*types.Transaction, error) {
	return _ERC721.Contract.PopulatePreSaleWhitelist(&_ERC721.TransactOpts, preSaleWalletAddresses)
}

// RenounceOwnership is a paid mutator transaction binding the contract method 0x715018a6.
//
// Solidity: function renounceOwnership() returns()
func (_ERC721 *ERC721Transactor) RenounceOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "renounceOwnership")
}

// RenounceOwnership is a paid mutator transaction binding the contract method 0x715018a6.
//
// Solidity: function renounceOwnership() returns()
func (_ERC721 *ERC721Session) RenounceOwnership() (*types.Transaction, error) {
	return _ERC721.Contract.RenounceOwnership(&_ERC721.TransactOpts)
}

// RenounceOwnership is a paid mutator transaction binding the contract method 0x715018a6.
//
// Solidity: function renounceOwnership() returns()
func (_ERC721 *ERC721TransactorSession) RenounceOwnership() (*types.Transaction, error) {
	return _ERC721.Contract.RenounceOwnership(&_ERC721.TransactOpts)
}

// SafeTransferFrom is a paid mutator transaction binding the contract method 0x42842e0e.
//
// Solidity: function safeTransferFrom(address from, address to, uint256 tokenId) returns()
func (_ERC721 *ERC721Transactor) SafeTransferFrom(opts *bind.TransactOpts, from common.Address, to common.Address, tokenId *big.Int) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "safeTransferFrom", from, to, tokenId)
}

// SafeTransferFrom is a paid mutator transaction binding the contract method 0x42842e0e.
//
// Solidity: function safeTransferFrom(address from, address to, uint256 tokenId) returns()
func (_ERC721 *ERC721Session) SafeTransferFrom(from common.Address, to common.Address, tokenId *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.SafeTransferFrom(&_ERC721.TransactOpts, from, to, tokenId)
}

// SafeTransferFrom is a paid mutator transaction binding the contract method 0x42842e0e.
//
// Solidity: function safeTransferFrom(address from, address to, uint256 tokenId) returns()
func (_ERC721 *ERC721TransactorSession) SafeTransferFrom(from common.Address, to common.Address, tokenId *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.SafeTransferFrom(&_ERC721.TransactOpts, from, to, tokenId)
}

// SafeTransferFrom0 is a paid mutator transaction binding the contract method 0xb88d4fde.
//
// Solidity: function safeTransferFrom(address from, address to, uint256 tokenId, bytes _data) returns()
func (_ERC721 *ERC721Transactor) SafeTransferFrom0(opts *bind.TransactOpts, from common.Address, to common.Address, tokenId *big.Int, _data []byte) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "safeTransferFrom0", from, to, tokenId, _data)
}

// SafeTransferFrom0 is a paid mutator transaction binding the contract method 0xb88d4fde.
//
// Solidity: function safeTransferFrom(address from, address to, uint256 tokenId, bytes _data) returns()
func (_ERC721 *ERC721Session) SafeTransferFrom0(from common.Address, to common.Address, tokenId *big.Int, _data []byte) (*types.Transaction, error) {
	return _ERC721.Contract.SafeTransferFrom0(&_ERC721.TransactOpts, from, to, tokenId, _data)
}

// SafeTransferFrom0 is a paid mutator transaction binding the contract method 0xb88d4fde.
//
// Solidity: function safeTransferFrom(address from, address to, uint256 tokenId, bytes _data) returns()
func (_ERC721 *ERC721TransactorSession) SafeTransferFrom0(from common.Address, to common.Address, tokenId *big.Int, _data []byte) (*types.Transaction, error) {
	return _ERC721.Contract.SafeTransferFrom0(&_ERC721.TransactOpts, from, to, tokenId, _data)
}

// SetApprovalForAll is a paid mutator transaction binding the contract method 0xa22cb465.
//
// Solidity: function setApprovalForAll(address operator, bool approved) returns()
func (_ERC721 *ERC721Transactor) SetApprovalForAll(opts *bind.TransactOpts, operator common.Address, approved bool) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "setApprovalForAll", operator, approved)
}

// SetApprovalForAll is a paid mutator transaction binding the contract method 0xa22cb465.
//
// Solidity: function setApprovalForAll(address operator, bool approved) returns()
func (_ERC721 *ERC721Session) SetApprovalForAll(operator common.Address, approved bool) (*types.Transaction, error) {
	return _ERC721.Contract.SetApprovalForAll(&_ERC721.TransactOpts, operator, approved)
}

// SetApprovalForAll is a paid mutator transaction binding the contract method 0xa22cb465.
//
// Solidity: function setApprovalForAll(address operator, bool approved) returns()
func (_ERC721 *ERC721TransactorSession) SetApprovalForAll(operator common.Address, approved bool) (*types.Transaction, error) {
	return _ERC721.Contract.SetApprovalForAll(&_ERC721.TransactOpts, operator, approved)
}

// SetUri is a paid mutator transaction binding the contract method 0x0a04472b.
//
// Solidity: function set_uri(string _baseURI) returns()
func (_ERC721 *ERC721Transactor) SetUri(opts *bind.TransactOpts, _baseURI string) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "set_uri", _baseURI)
}

// SetUri is a paid mutator transaction binding the contract method 0x0a04472b.
//
// Solidity: function set_uri(string _baseURI) returns()
func (_ERC721 *ERC721Session) SetUri(_baseURI string) (*types.Transaction, error) {
	return _ERC721.Contract.SetUri(&_ERC721.TransactOpts, _baseURI)
}

// SetUri is a paid mutator transaction binding the contract method 0x0a04472b.
//
// Solidity: function set_uri(string _baseURI) returns()
func (_ERC721 *ERC721TransactorSession) SetUri(_baseURI string) (*types.Transaction, error) {
	return _ERC721.Contract.SetUri(&_ERC721.TransactOpts, _baseURI)
}

// SetsumonCost is a paid mutator transaction binding the contract method 0x6a5c1957.
//
// Solidity: function setsumonCost(uint256 newCost) returns()
func (_ERC721 *ERC721Transactor) SetsumonCost(opts *bind.TransactOpts, newCost *big.Int) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "setsumonCost", newCost)
}

// SetsumonCost is a paid mutator transaction binding the contract method 0x6a5c1957.
//
// Solidity: function setsumonCost(uint256 newCost) returns()
func (_ERC721 *ERC721Session) SetsumonCost(newCost *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.SetsumonCost(&_ERC721.TransactOpts, newCost)
}

// SetsumonCost is a paid mutator transaction binding the contract method 0x6a5c1957.
//
// Solidity: function setsumonCost(uint256 newCost) returns()
func (_ERC721 *ERC721TransactorSession) SetsumonCost(newCost *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.SetsumonCost(&_ERC721.TransactOpts, newCost)
}

// SetsumonSupply is a paid mutator transaction binding the contract method 0x5a8c2fe6.
//
// Solidity: function setsumonSupply(uint256 newsumonSupply) returns()
func (_ERC721 *ERC721Transactor) SetsumonSupply(opts *bind.TransactOpts, newsumonSupply *big.Int) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "setsumonSupply", newsumonSupply)
}

// SetsumonSupply is a paid mutator transaction binding the contract method 0x5a8c2fe6.
//
// Solidity: function setsumonSupply(uint256 newsumonSupply) returns()
func (_ERC721 *ERC721Session) SetsumonSupply(newsumonSupply *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.SetsumonSupply(&_ERC721.TransactOpts, newsumonSupply)
}

// SetsumonSupply is a paid mutator transaction binding the contract method 0x5a8c2fe6.
//
// Solidity: function setsumonSupply(uint256 newsumonSupply) returns()
func (_ERC721 *ERC721TransactorSession) SetsumonSupply(newsumonSupply *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.SetsumonSupply(&_ERC721.TransactOpts, newsumonSupply)
}

// Sumon is a paid mutator transaction binding the contract method 0x1d91b2ae.
//
// Solidity: function sumon(uint256[] token_ids) returns()
func (_ERC721 *ERC721Transactor) Sumon(opts *bind.TransactOpts, token_ids []*big.Int) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "sumon", token_ids)
}

// Sumon is a paid mutator transaction binding the contract method 0x1d91b2ae.
//
// Solidity: function sumon(uint256[] token_ids) returns()
func (_ERC721 *ERC721Session) Sumon(token_ids []*big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.Sumon(&_ERC721.TransactOpts, token_ids)
}

// Sumon is a paid mutator transaction binding the contract method 0x1d91b2ae.
//
// Solidity: function sumon(uint256[] token_ids) returns()
func (_ERC721 *ERC721TransactorSession) Sumon(token_ids []*big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.Sumon(&_ERC721.TransactOpts, token_ids)
}

// SumonAmount is a paid mutator transaction binding the contract method 0x8024e6f3.
//
// Solidity: function sumonAmount(uint256 _supplyLimit) returns()
func (_ERC721 *ERC721Transactor) SumonAmount(opts *bind.TransactOpts, _supplyLimit *big.Int) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "sumonAmount", _supplyLimit)
}

// SumonAmount is a paid mutator transaction binding the contract method 0x8024e6f3.
//
// Solidity: function sumonAmount(uint256 _supplyLimit) returns()
func (_ERC721 *ERC721Session) SumonAmount(_supplyLimit *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.SumonAmount(&_ERC721.TransactOpts, _supplyLimit)
}

// SumonAmount is a paid mutator transaction binding the contract method 0x8024e6f3.
//
// Solidity: function sumonAmount(uint256 _supplyLimit) returns()
func (_ERC721 *ERC721TransactorSession) SumonAmount(_supplyLimit *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.SumonAmount(&_ERC721.TransactOpts, _supplyLimit)
}

// SumonMaxGoatMint is a paid mutator transaction binding the contract method 0x17b6b8eb.
//
// Solidity: function sumon_maxGoatMint(uint256 _mintLimit) returns()
func (_ERC721 *ERC721Transactor) SumonMaxGoatMint(opts *bind.TransactOpts, _mintLimit *big.Int) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "sumon_maxGoatMint", _mintLimit)
}

// SumonMaxGoatMint is a paid mutator transaction binding the contract method 0x17b6b8eb.
//
// Solidity: function sumon_maxGoatMint(uint256 _mintLimit) returns()
func (_ERC721 *ERC721Session) SumonMaxGoatMint(_mintLimit *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.SumonMaxGoatMint(&_ERC721.TransactOpts, _mintLimit)
}

// SumonMaxGoatMint is a paid mutator transaction binding the contract method 0x17b6b8eb.
//
// Solidity: function sumon_maxGoatMint(uint256 _mintLimit) returns()
func (_ERC721 *ERC721TransactorSession) SumonMaxGoatMint(_mintLimit *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.SumonMaxGoatMint(&_ERC721.TransactOpts, _mintLimit)
}

// SumonMintPrice is a paid mutator transaction binding the contract method 0x1a12c13d.
//
// Solidity: function sumon_mintPrice(uint256 _mintPrice) returns()
func (_ERC721 *ERC721Transactor) SumonMintPrice(opts *bind.TransactOpts, _mintPrice *big.Int) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "sumon_mintPrice", _mintPrice)
}

// SumonMintPrice is a paid mutator transaction binding the contract method 0x1a12c13d.
//
// Solidity: function sumon_mintPrice(uint256 _mintPrice) returns()
func (_ERC721 *ERC721Session) SumonMintPrice(_mintPrice *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.SumonMintPrice(&_ERC721.TransactOpts, _mintPrice)
}

// SumonMintPrice is a paid mutator transaction binding the contract method 0x1a12c13d.
//
// Solidity: function sumon_mintPrice(uint256 _mintPrice) returns()
func (_ERC721 *ERC721TransactorSession) SumonMintPrice(_mintPrice *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.SumonMintPrice(&_ERC721.TransactOpts, _mintPrice)
}

// TransferFrom is a paid mutator transaction binding the contract method 0x23b872dd.
//
// Solidity: function transferFrom(address from, address to, uint256 tokenId) returns()
func (_ERC721 *ERC721Transactor) TransferFrom(opts *bind.TransactOpts, from common.Address, to common.Address, tokenId *big.Int) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "transferFrom", from, to, tokenId)
}

// TransferFrom is a paid mutator transaction binding the contract method 0x23b872dd.
//
// Solidity: function transferFrom(address from, address to, uint256 tokenId) returns()
func (_ERC721 *ERC721Session) TransferFrom(from common.Address, to common.Address, tokenId *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.TransferFrom(&_ERC721.TransactOpts, from, to, tokenId)
}

// TransferFrom is a paid mutator transaction binding the contract method 0x23b872dd.
//
// Solidity: function transferFrom(address from, address to, uint256 tokenId) returns()
func (_ERC721 *ERC721TransactorSession) TransferFrom(from common.Address, to common.Address, tokenId *big.Int) (*types.Transaction, error) {
	return _ERC721.Contract.TransferFrom(&_ERC721.TransactOpts, from, to, tokenId)
}

// TransferOwnership is a paid mutator transaction binding the contract method 0xf2fde38b.
//
// Solidity: function transferOwnership(address newOwner) returns()
func (_ERC721 *ERC721Transactor) TransferOwnership(opts *bind.TransactOpts, newOwner common.Address) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "transferOwnership", newOwner)
}

// TransferOwnership is a paid mutator transaction binding the contract method 0xf2fde38b.
//
// Solidity: function transferOwnership(address newOwner) returns()
func (_ERC721 *ERC721Session) TransferOwnership(newOwner common.Address) (*types.Transaction, error) {
	return _ERC721.Contract.TransferOwnership(&_ERC721.TransactOpts, newOwner)
}

// TransferOwnership is a paid mutator transaction binding the contract method 0xf2fde38b.
//
// Solidity: function transferOwnership(address newOwner) returns()
func (_ERC721 *ERC721TransactorSession) TransferOwnership(newOwner common.Address) (*types.Transaction, error) {
	return _ERC721.Contract.TransferOwnership(&_ERC721.TransactOpts, newOwner)
}

// Withdraw is a paid mutator transaction binding the contract method 0x3ccfd60b.
//
// Solidity: function withdraw() returns()
func (_ERC721 *ERC721Transactor) Withdraw(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "withdraw")
}

// Withdraw is a paid mutator transaction binding the contract method 0x3ccfd60b.
//
// Solidity: function withdraw() returns()
func (_ERC721 *ERC721Session) Withdraw() (*types.Transaction, error) {
	return _ERC721.Contract.Withdraw(&_ERC721.TransactOpts)
}

// Withdraw is a paid mutator transaction binding the contract method 0x3ccfd60b.
//
// Solidity: function withdraw() returns()
func (_ERC721 *ERC721TransactorSession) Withdraw() (*types.Transaction, error) {
	return _ERC721.Contract.Withdraw(&_ERC721.TransactOpts)
}

// WithdrawES is a paid mutator transaction binding the contract method 0xae2b7f22.
//
// Solidity: function withdrawES() returns()
func (_ERC721 *ERC721Transactor) WithdrawES(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _ERC721.contract.Transact(opts, "withdrawES")
}

// WithdrawES is a paid mutator transaction binding the contract method 0xae2b7f22.
//
// Solidity: function withdrawES() returns()
func (_ERC721 *ERC721Session) WithdrawES() (*types.Transaction, error) {
	return _ERC721.Contract.WithdrawES(&_ERC721.TransactOpts)
}

// WithdrawES is a paid mutator transaction binding the contract method 0xae2b7f22.
//
// Solidity: function withdrawES() returns()
func (_ERC721 *ERC721TransactorSession) WithdrawES() (*types.Transaction, error) {
	return _ERC721.Contract.WithdrawES(&_ERC721.TransactOpts)
}

// ERC721ApprovalIterator is returned from FilterApproval and is used to iterate over the raw logs and unpacked data for Approval events raised by the ERC721 contract.
type ERC721ApprovalIterator struct {
	Event *ERC721Approval // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *ERC721ApprovalIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(ERC721Approval)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(ERC721Approval)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *ERC721ApprovalIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *ERC721ApprovalIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// ERC721Approval represents a Approval event raised by the ERC721 contract.
type ERC721Approval struct {
	Owner    common.Address
	Approved common.Address
	TokenId  *big.Int
	Raw      types.Log // Blockchain specific contextual infos
}

// FilterApproval is a free log retrieval operation binding the contract event 0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925.
//
// Solidity: event Approval(address indexed owner, address indexed approved, uint256 indexed tokenId)
func (_ERC721 *ERC721Filterer) FilterApproval(opts *bind.FilterOpts, owner []common.Address, approved []common.Address, tokenId []*big.Int) (*ERC721ApprovalIterator, error) {

	var ownerRule []interface{}
	for _, ownerItem := range owner {
		ownerRule = append(ownerRule, ownerItem)
	}
	var approvedRule []interface{}
	for _, approvedItem := range approved {
		approvedRule = append(approvedRule, approvedItem)
	}
	var tokenIdRule []interface{}
	for _, tokenIdItem := range tokenId {
		tokenIdRule = append(tokenIdRule, tokenIdItem)
	}

	logs, sub, err := _ERC721.contract.FilterLogs(opts, "Approval", ownerRule, approvedRule, tokenIdRule)
	if err != nil {
		return nil, err
	}
	return &ERC721ApprovalIterator{contract: _ERC721.contract, event: "Approval", logs: logs, sub: sub}, nil
}

// WatchApproval is a free log subscription operation binding the contract event 0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925.
//
// Solidity: event Approval(address indexed owner, address indexed approved, uint256 indexed tokenId)
func (_ERC721 *ERC721Filterer) WatchApproval(opts *bind.WatchOpts, sink chan<- *ERC721Approval, owner []common.Address, approved []common.Address, tokenId []*big.Int) (event.Subscription, error) {

	var ownerRule []interface{}
	for _, ownerItem := range owner {
		ownerRule = append(ownerRule, ownerItem)
	}
	var approvedRule []interface{}
	for _, approvedItem := range approved {
		approvedRule = append(approvedRule, approvedItem)
	}
	var tokenIdRule []interface{}
	for _, tokenIdItem := range tokenId {
		tokenIdRule = append(tokenIdRule, tokenIdItem)
	}

	logs, sub, err := _ERC721.contract.WatchLogs(opts, "Approval", ownerRule, approvedRule, tokenIdRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(ERC721Approval)
				if err := _ERC721.contract.UnpackLog(event, "Approval", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseApproval is a log parse operation binding the contract event 0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925.
//
// Solidity: event Approval(address indexed owner, address indexed approved, uint256 indexed tokenId)
func (_ERC721 *ERC721Filterer) ParseApproval(log types.Log) (*ERC721Approval, error) {
	event := new(ERC721Approval)
	if err := _ERC721.contract.UnpackLog(event, "Approval", log); err != nil {
		return nil, err
	}
	return event, nil
}

// ERC721ApprovalForAllIterator is returned from FilterApprovalForAll and is used to iterate over the raw logs and unpacked data for ApprovalForAll events raised by the ERC721 contract.
type ERC721ApprovalForAllIterator struct {
	Event *ERC721ApprovalForAll // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *ERC721ApprovalForAllIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(ERC721ApprovalForAll)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(ERC721ApprovalForAll)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *ERC721ApprovalForAllIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *ERC721ApprovalForAllIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// ERC721ApprovalForAll represents a ApprovalForAll event raised by the ERC721 contract.
type ERC721ApprovalForAll struct {
	Owner    common.Address
	Operator common.Address
	Approved bool
	Raw      types.Log // Blockchain specific contextual infos
}

// FilterApprovalForAll is a free log retrieval operation binding the contract event 0x17307eab39ab6107e8899845ad3d59bd9653f200f220920489ca2b5937696c31.
//
// Solidity: event ApprovalForAll(address indexed owner, address indexed operator, bool approved)
func (_ERC721 *ERC721Filterer) FilterApprovalForAll(opts *bind.FilterOpts, owner []common.Address, operator []common.Address) (*ERC721ApprovalForAllIterator, error) {

	var ownerRule []interface{}
	for _, ownerItem := range owner {
		ownerRule = append(ownerRule, ownerItem)
	}
	var operatorRule []interface{}
	for _, operatorItem := range operator {
		operatorRule = append(operatorRule, operatorItem)
	}

	logs, sub, err := _ERC721.contract.FilterLogs(opts, "ApprovalForAll", ownerRule, operatorRule)
	if err != nil {
		return nil, err
	}
	return &ERC721ApprovalForAllIterator{contract: _ERC721.contract, event: "ApprovalForAll", logs: logs, sub: sub}, nil
}

// WatchApprovalForAll is a free log subscription operation binding the contract event 0x17307eab39ab6107e8899845ad3d59bd9653f200f220920489ca2b5937696c31.
//
// Solidity: event ApprovalForAll(address indexed owner, address indexed operator, bool approved)
func (_ERC721 *ERC721Filterer) WatchApprovalForAll(opts *bind.WatchOpts, sink chan<- *ERC721ApprovalForAll, owner []common.Address, operator []common.Address) (event.Subscription, error) {

	var ownerRule []interface{}
	for _, ownerItem := range owner {
		ownerRule = append(ownerRule, ownerItem)
	}
	var operatorRule []interface{}
	for _, operatorItem := range operator {
		operatorRule = append(operatorRule, operatorItem)
	}

	logs, sub, err := _ERC721.contract.WatchLogs(opts, "ApprovalForAll", ownerRule, operatorRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(ERC721ApprovalForAll)
				if err := _ERC721.contract.UnpackLog(event, "ApprovalForAll", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseApprovalForAll is a log parse operation binding the contract event 0x17307eab39ab6107e8899845ad3d59bd9653f200f220920489ca2b5937696c31.
//
// Solidity: event ApprovalForAll(address indexed owner, address indexed operator, bool approved)
func (_ERC721 *ERC721Filterer) ParseApprovalForAll(log types.Log) (*ERC721ApprovalForAll, error) {
	event := new(ERC721ApprovalForAll)
	if err := _ERC721.contract.UnpackLog(event, "ApprovalForAll", log); err != nil {
		return nil, err
	}
	return event, nil
}

// ERC721OwnershipTransferredIterator is returned from FilterOwnershipTransferred and is used to iterate over the raw logs and unpacked data for OwnershipTransferred events raised by the ERC721 contract.
type ERC721OwnershipTransferredIterator struct {
	Event *ERC721OwnershipTransferred // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *ERC721OwnershipTransferredIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(ERC721OwnershipTransferred)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(ERC721OwnershipTransferred)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *ERC721OwnershipTransferredIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *ERC721OwnershipTransferredIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// ERC721OwnershipTransferred represents a OwnershipTransferred event raised by the ERC721 contract.
type ERC721OwnershipTransferred struct {
	PreviousOwner common.Address
	NewOwner      common.Address
	Raw           types.Log // Blockchain specific contextual infos
}

// FilterOwnershipTransferred is a free log retrieval operation binding the contract event 0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0.
//
// Solidity: event OwnershipTransferred(address indexed previousOwner, address indexed newOwner)
func (_ERC721 *ERC721Filterer) FilterOwnershipTransferred(opts *bind.FilterOpts, previousOwner []common.Address, newOwner []common.Address) (*ERC721OwnershipTransferredIterator, error) {

	var previousOwnerRule []interface{}
	for _, previousOwnerItem := range previousOwner {
		previousOwnerRule = append(previousOwnerRule, previousOwnerItem)
	}
	var newOwnerRule []interface{}
	for _, newOwnerItem := range newOwner {
		newOwnerRule = append(newOwnerRule, newOwnerItem)
	}

	logs, sub, err := _ERC721.contract.FilterLogs(opts, "OwnershipTransferred", previousOwnerRule, newOwnerRule)
	if err != nil {
		return nil, err
	}
	return &ERC721OwnershipTransferredIterator{contract: _ERC721.contract, event: "OwnershipTransferred", logs: logs, sub: sub}, nil
}

// WatchOwnershipTransferred is a free log subscription operation binding the contract event 0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0.
//
// Solidity: event OwnershipTransferred(address indexed previousOwner, address indexed newOwner)
func (_ERC721 *ERC721Filterer) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *ERC721OwnershipTransferred, previousOwner []common.Address, newOwner []common.Address) (event.Subscription, error) {

	var previousOwnerRule []interface{}
	for _, previousOwnerItem := range previousOwner {
		previousOwnerRule = append(previousOwnerRule, previousOwnerItem)
	}
	var newOwnerRule []interface{}
	for _, newOwnerItem := range newOwner {
		newOwnerRule = append(newOwnerRule, newOwnerItem)
	}

	logs, sub, err := _ERC721.contract.WatchLogs(opts, "OwnershipTransferred", previousOwnerRule, newOwnerRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(ERC721OwnershipTransferred)
				if err := _ERC721.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseOwnershipTransferred is a log parse operation binding the contract event 0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0.
//
// Solidity: event OwnershipTransferred(address indexed previousOwner, address indexed newOwner)
func (_ERC721 *ERC721Filterer) ParseOwnershipTransferred(log types.Log) (*ERC721OwnershipTransferred, error) {
	event := new(ERC721OwnershipTransferred)
	if err := _ERC721.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
		return nil, err
	}
	return event, nil
}

// ERC721TransferIterator is returned from FilterTransfer and is used to iterate over the raw logs and unpacked data for Transfer events raised by the ERC721 contract.
type ERC721TransferIterator struct {
	Event *ERC721Transfer // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *ERC721TransferIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(ERC721Transfer)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(ERC721Transfer)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *ERC721TransferIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *ERC721TransferIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// ERC721Transfer represents a Transfer event raised by the ERC721 contract.
type ERC721Transfer struct {
	From    common.Address
	To      common.Address
	TokenId *big.Int
	Raw     types.Log // Blockchain specific contextual infos
}

// FilterTransfer is a free log retrieval operation binding the contract event 0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef.
//
// Solidity: event Transfer(address indexed from, address indexed to, uint256 indexed tokenId)
func (_ERC721 *ERC721Filterer) FilterTransfer(opts *bind.FilterOpts, from []common.Address, to []common.Address, tokenId []*big.Int) (*ERC721TransferIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}
	var tokenIdRule []interface{}
	for _, tokenIdItem := range tokenId {
		tokenIdRule = append(tokenIdRule, tokenIdItem)
	}

	logs, sub, err := _ERC721.contract.FilterLogs(opts, "Transfer", fromRule, toRule, tokenIdRule)
	if err != nil {
		return nil, err
	}
	return &ERC721TransferIterator{contract: _ERC721.contract, event: "Transfer", logs: logs, sub: sub}, nil
}

// WatchTransfer is a free log subscription operation binding the contract event 0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef.
//
// Solidity: event Transfer(address indexed from, address indexed to, uint256 indexed tokenId)
func (_ERC721 *ERC721Filterer) WatchTransfer(opts *bind.WatchOpts, sink chan<- *ERC721Transfer, from []common.Address, to []common.Address, tokenId []*big.Int) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}
	var tokenIdRule []interface{}
	for _, tokenIdItem := range tokenId {
		tokenIdRule = append(tokenIdRule, tokenIdItem)
	}

	logs, sub, err := _ERC721.contract.WatchLogs(opts, "Transfer", fromRule, toRule, tokenIdRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(ERC721Transfer)
				if err := _ERC721.contract.UnpackLog(event, "Transfer", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseTransfer is a log parse operation binding the contract event 0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef.
//
// Solidity: event Transfer(address indexed from, address indexed to, uint256 indexed tokenId)
func (_ERC721 *ERC721Filterer) ParseTransfer(log types.Log) (*ERC721Transfer, error) {
	event := new(ERC721Transfer)
	if err := _ERC721.contract.UnpackLog(event, "Transfer", log); err != nil {
		return nil, err
	}
	return event, nil
}
