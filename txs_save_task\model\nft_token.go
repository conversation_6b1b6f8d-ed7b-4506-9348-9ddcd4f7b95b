package model

import (
	"encoding/json"

	"go.mongodb.org/mongo-driver/bson"
)

type NftTokenInfo struct {
	ContractAddress string `json:"contractAddress"`
	TokenId         string `json:"tokenId"`
	OwnerAddress    string `json:"ownerAddress"`
	BlockHash       string `json:"blockHash"`
	BlockNumber     uint64 `json:"blockNumber"`
}

func (s *NftTokenInfo) GetUniqueKeys() map[string]interface{} {
	return bson.M{"blockHash": s.BlockHash, "contractAddress": s.<PERSON>tract<PERSON>ddress, "tokenId": s.TokenId}
}

func (s NftTokenInfo) String() string {
	marshal, _ := json.Marshal(s)
	return string(marshal)
}
