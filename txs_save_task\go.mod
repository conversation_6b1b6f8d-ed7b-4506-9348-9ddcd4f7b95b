module github.com/tokenbankteam/txs_save_task

go 1.12

require (
	github.com/Shopify/sarama v1.18.0
	github.com/Shopify/toxiproxy v2.1.4+incompatible // indirect
	github.com/bluele/gcache v0.0.2
	github.com/bsm/sarama-cluster v2.1.15+incompatible
	github.com/cihub/seelog v0.0.0-**************-f561c5e57575
	github.com/eapache/go-resiliency v1.1.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-**************-776d5712da21 // indirect
	github.com/eapache/queue v0.0.0-**************-093482f3f8ce // indirect
	github.com/elazarl/goproxy v0.0.0-**************-a92cc753f88e // indirect
	github.com/ethereum/go-ethereum v1.9.25
	github.com/frankban/quicktest v1.14.3 // indirect
	github.com/go-redis/redis v6.14.1+incompatible // indirect
	github.com/go-sql-driver/mysql v1.5.0 // indirect
	github.com/jinzhu/configor v1.0.0
	github.com/json-iterator/go v1.1.12
	github.com/lib/pq v1.10.0 // indirect
	github.com/moul/http2curl v1.0.0 // indirect
	github.com/parnurzeal/gorequest v0.2.15
	github.com/pierrec/lz4 v2.6.0+incompatible // indirect
	github.com/prometheus/client_golang v1.12.1
	github.com/rcrowley/go-metrics v0.0.0-**************-e2704e165165 // indirect
	github.com/shopspring/decimal v1.3.1
	github.com/smartystreets/goconvey v1.7.2 // indirect
	github.com/stretchr/testify v1.7.1
	github.com/tokenbankteam/tb_common v0.2.0
	go.mongodb.org/mongo-driver v1.12.1
	go.uber.org/automaxprocs v1.5.1
	gotest.tools v2.2.0+incompatible
)

replace (
	cloud.google.com/go => github.com/googleapis/google-cloud-go v0.41.1-0.**************-73df346b330e
	github.com/tendermint/go-amino => github.com/binance-chain/bnc-go-amino v0.14.1-binance.1
	golang.org/x/crypto => github.com/golang/crypto v0.0.0-**************-a1f597ede03a
	golang.org/x/exp => github.com/golang/exp v0.0.0-**************-fd42eb6b336f
	golang.org/x/image => github.com/golang/image v0.0.0-**************-d6a02ce849c9
	golang.org/x/lint => github.com/golang/lint v0.0.0-**************-959b441ac422
	golang.org/x/mobile => github.com/golang/mobile v0.0.0-**************-e47acb2ca7f9
	golang.org/x/net => github.com/golang/net v0.0.0-**************-d196dffd7c2b
	golang.org/x/oauth2 => github.com/golang/oauth2 v0.0.0-**************-aaccbc9213b0
	golang.org/x/sync => github.com/golang/sync v0.0.0-**************-e225da77a7e6
	golang.org/x/sys => github.com/golang/sys v0.0.0-20190318195719-6c81ef8f67ca
	golang.org/x/text => github.com/golang/text v0.3.0
	golang.org/x/time => github.com/golang/time v0.0.0-20190308202827-9d24e82272b4
	golang.org/x/tools => github.com/golang/tools v0.0.0-20190529010454-aa71c3f32488
	google.golang.org/api => github.com/googleapis/google-api-go-client v0.7.1-0.20190712000834-aa15faf3c8a1
	google.golang.org/appengine => github.com/golang/appengine v1.6.1-0.20190515044707-311d3c5cf937
	google.golang.org/genproto => github.com/google/go-genproto v0.0.0-20190522204451-c2c4e71fbf69
	google.golang.org/grpc => github.com/grpc/grpc-go v1.21.0
)
