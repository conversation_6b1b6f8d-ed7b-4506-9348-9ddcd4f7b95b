package utils

const (
	Failed  = 0
	SUCCESS = 1
	Pending = 2
	Unknown = 99

	UnFork = 0
	Fork   = 1

	Token = 1 // 代币

	InputErc20Transfer       = "0xa9059cbb"
	InputErc721TransferFrom  = "0x23b872dd"
	InputErc1155TransferFrom = "0xf242432a"

	// EventSignTransfer keccak256("Transfer(address,address,uint256)")
	EventSignTransfer = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"
	// EventSignPunkTransfer keccak256(PunkTransfer(address,address,uint256)
	EventSignPunkTransfer = "0x05af636b70da6819000c49f85b21fa82081c632069bb626f30932034099107d8"
	// EventSignPunkBought PunkBought (index_topic_1 uint256 punkIndex, uint256 value, index_topic_2 address fromAddress, index_topic_3 address toAddress)
	EventSignPunkBought = "0x58e5d5a525e3b40bc15abaa38b5882678db1ee68befd2f60bafe3a7fd06db9e3"
	// EventSignAssign Assign(address indexed to, uint256 punkIndex)
	EventSignAssign = "0x8a0e37b73a0d9c82e205d4d1a3ff3d0b57ce5f4d7bccf6bac03336dc101cb7ba"

	// EventTransferBatch event 0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb.
	// Solidity: event TransferBatch(address indexed _operator, address indexed _from, address indexed _to, uint256[] _ids, uint256[] _values)
	EventTransferBatch = "0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb"

	// EventTransferSingle event 0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62.
	// Solidity: event TransferSingle(address indexed _operator, address indexed _from, address indexed _to, uint256 _id, uint256 _value)
	EventTransferSingle = "0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62"

	Erc20TopicSize                 = 3
	Erc721TopicSize                = 4
	Erc1155TransferSingleTopicSize = 4
	Erc1155TransferBatchTopicSize  = 4
)

const (
	TokenProtocolErc20   int64 = 0
	TokenProtocolErc721  int64 = 1
	TokenProtocolErc1155 int64 = 2
)
