package utils

import (
	"encoding/json"
	log "github.com/cihub/seelog"
	"github.com/parnurzeal/gorequest"
	"testing"
)

func TestSendEmail(t *testing.T) {
	addr := "http://emailinternal.mytokenpocket.vip/v1/email"
	//addr := "http://localhost:8080/v1/email"
	resp, body, errs := gorequest.New().Post(addr).Type("form").SendMap(
		map[string]string{
			"to":      "<EMAIL>",
			"subject": "test",
			"body":    "this is a test email",
		}).End()
	if errs != nil && len(errs) > 0 && errs[0] != nil {
		var err error
		for _, err = range errs {
			log.Errorf("request %v error, %v", addr, err)
		}
	} else if resp.StatusCode != 200 {
		log.Errorf("request %v result invalid, status %v, body %v", addr, resp.StatusCode, body)
	}
	defer resp.Body.Close()
	type Result struct {
		Result  int64  `json:"result"`
		Message string `json:"message"`
		Data    int64  `json:"data"`
	}
	ret := Result{}
	if err := json.Unmarshal([]byte(body), ret); err != nil {
		log.Errorf("error, %v", err)
	}
}
