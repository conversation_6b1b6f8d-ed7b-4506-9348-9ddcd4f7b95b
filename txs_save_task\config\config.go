package config

import (
	cp "github.com/tokenbankteam/tb_common/cache"
	dbp "github.com/tokenbankteam/tb_common/db"
)

type AppConfig struct {
	Profile      string   `default:"dev"`
	Monitor      bool     `default:"true"`
	MonitorAddrs []string `default:"localhost:7272"`
	PprofAddrs   []string `default:"localhost:7271"`
	Addr         string   `default:"0.0.0.0:8080"`
	PushGateway  string
	PushJobName  string
	DB           dbp.DBConfig
	Collection   string
	Redis        cp.RedisConfig
	Logger       string `default:"conf/logger.xml"`

	MongoAddr                   string `default:"mongodb://*************:27017"`
	Database                    string
	PendingDatabase             string
	PendingDatabaseCollection   string
	PendingDatabaseCollectionV2 string

	Pending                      bool
	BrokerList                   []string
	TrxSaveConsumerGroup         string
	TrxSaveTopic                 string
	WalletTokenTopic             string
	PendingTrxTopic              string
	PendingToleranceIntervalTime int64 //pending容忍的偏差单位秒
	BlockChain                   string
	BlockChainId                 int64

	BatchSize      int `default:"100"`  // 批量处理消息数量
	BatchTimeoutMs int `default:"1000"` // 批量处理超时时间(毫秒)
}
