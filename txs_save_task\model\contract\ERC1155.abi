[{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "_operator", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "_approved", "type": "bool"}], "name": "ApprovalForAll", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_operator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "_from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "_to", "type": "address"}, {"indexed": false, "internalType": "uint256[]", "name": "_ids", "type": "uint256[]"}, {"indexed": false, "internalType": "uint256[]", "name": "_values", "type": "uint256[]"}], "name": "TransferBatch", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_operator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "_from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "_to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "_value", "type": "uint256"}], "name": "TransferSingle", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "_value", "type": "string"}, {"indexed": true, "internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "URI", "type": "event"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_from", "type": "address"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "_id", "type": "uint256"}, {"internalType": "uint256", "name": "_value", "type": "uint256"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "name": "safeTransferFrom", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_from", "type": "address"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint256[]", "name": "_ids", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "_values", "type": "uint256[]"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "name": "safeBatchTransferFrom", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "uint256", "name": "_id", "type": "uint256"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address[]", "name": "_owners", "type": "address[]"}, {"internalType": "uint256[]", "name": "_ids", "type": "uint256[]"}], "name": "balanceOfBatch", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "_operator", "type": "address"}, {"internalType": "bool", "name": "_approved", "type": "bool"}], "name": "setApprovalForAll", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "_owner", "type": "address"}, {"internalType": "address", "name": "_operator", "type": "address"}], "name": "isApprovedForAll", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}]