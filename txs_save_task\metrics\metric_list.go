package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/tokenbankteam/tb_common/metrics"
)

var GasPriceSummaryVec = prometheus.NewSummaryVec(prometheus.SummaryOpts{
	Name:       "gas_on_chain_fee_summary",
	Help:       "gas on-chain fee summary partitioned by blockchain id",
	Objectives: map[float64]float64{0.01: 0.001, 0.05: 0.005, 0.10: 0.01, 0.25: 0.025, 0.50: 0.05, 0.75: 0.025, 0.90: 0.01, 0.95: 0.005, 0.99: 0.001},
}, []string{"blockchain_id", "fee_type"})

func init() {
	metrics.DefaultRegistry.Register(GasPriceSummaryVec)
}

var SaveTaskErrorCounter = prometheus.NewCounterVec(prometheus.CounterOpts{
	Name: "task_save_trx_error_count",
	Help: "count of error in save trx task.",
}, []string{"blockchain_id", "blockchain"})

func init() {
	metrics.DefaultRegistry.Register(SaveTaskErrorCounter)
}

var SaveTaskMappingErrorCounter = prometheus.NewCounterVec(prometheus.CounterOpts{
	Name: "task_save_trx_mapping_error_count",
	Help: "count of mapping error in save trx task.",
}, []string{"blockchain_id", "blockchain", "reason"})

func init() {
	metrics.DefaultRegistry.Register(SaveTaskMappingErrorCounter)
}

var SaveTaskHealthCounter = prometheus.NewCounterVec(prometheus.CounterOpts{
	Name: "task_save_trx_health_count",
	Help: "count of health in save trx task.",
}, []string{"blockchain_id", "blockchain"})

func init() {
	metrics.DefaultRegistry.Register(SaveTaskHealthCounter)
}

var SaveTaskForkCounter = prometheus.NewCounterVec(prometheus.CounterOpts{
	Name: "task_save_trx_fork_count",
	Help: "count of fork block in save trx task.",
}, []string{"blockchain_id", "blockchain"})

func init() {
	metrics.DefaultRegistry.Register(SaveTaskForkCounter)
}

// SaveTaskSubTxSummaryVec 统计平铺开的交易数量  只统计成功的
var SaveTaskSubTxSummaryVec = prometheus.NewSummaryVec(prometheus.SummaryOpts{
	Name:       "task_save_trx_sub_tx_summary",
	Help:       "summary of transaction list in save trx task.",
	Objectives: map[float64]float64{0.01: 0.001, 0.05: 0.005, 0.10: 0.01, 0.25: 0.025, 0.50: 0.05, 0.75: 0.025, 0.90: 0.01, 0.95: 0.005, 0.99: 0.001},
}, []string{"blockchain_id"})

func init() {
	metrics.DefaultRegistry.Register(SaveTaskSubTxSummaryVec)
}

// SaveTaskMongoWriteSummaryVec 统计mongo写入性能  只统计成功的
var SaveTaskMongoWriteSummaryVec = prometheus.NewSummaryVec(prometheus.SummaryOpts{
	Name:       "task_save_trx_mongo_write_summary",
	Help:       "summary of mongo write in save trx task.",
	Objectives: map[float64]float64{0.01: 0.001, 0.05: 0.005, 0.10: 0.01, 0.25: 0.025, 0.50: 0.05, 0.75: 0.025, 0.90: 0.01, 0.95: 0.005, 0.99: 0.001},
}, []string{"blockchain_id", "type"})

func init() {
	metrics.DefaultRegistry.Register(SaveTaskMongoWriteSummaryVec)
}

// SaveTaskMappingTransactionsSummaryVec 统计MappingTransactionList性能  只统计成功的
var SaveTaskMappingTransactionsSummaryVec = prometheus.NewSummaryVec(prometheus.SummaryOpts{
	Name:       "task_save_mapping_cost_summary",
	Help:       "gid cost summary partitioned by blockchain id",
	Objectives: map[float64]float64{0.01: 0.001, 0.05: 0.005, 0.10: 0.01, 0.25: 0.025, 0.50: 0.05, 0.75: 0.025, 0.90: 0.01, 0.95: 0.005, 0.99: 0.001},
}, []string{"blockchain_id"})

func init() {
	metrics.DefaultRegistry.Register(SaveTaskMappingTransactionsSummaryVec)
}
