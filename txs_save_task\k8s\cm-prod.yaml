kind: ConfigMap
apiVersion: v1
metadata:
  name: cm-TP_TARGET
  namespace: tp-bserver
data:
  config_prod.yml: |
    # 系统环境
    profile: prod
    
    # 是否监控
    monitor: true
    # PprofAddrs *************:7272,********:7272
    monitoraddrs: [ '0.0.0.0:15574' ]
    
    # PprofAddrs *************:7271,********:7271
    pprofaddrs: [ '0.0.0.0:15573' ]
    
    # 服务监听地址
    addr: '0.0.0.0:15575'
    
    pushgateway: 'http://tppushgateway.mytokenpocket.vip'
    pushjobname: 'task_save_trx_base_full'
    
    # 日志配置文件
    logger: config/logger.xml
  
    
    mongoaddr: 'mgs1internal.mytokenpocket.vip:27017,mgs2internal.mytokenpocket.vip:27017,mgs3internal.mytokenpocket.vip:27017'
    database: 'base'
    collection: 'full_txs'
    pending: false
    
    brokerlist: [ '*************:9092', '*************:9092', '*************:9092' ]
    trxsavetopic: 'base_direct_trx_full'
    wallettokentopic: 'wallet_token_topic'
    trxsaveconsumergroup: 'task_save_trx_full_base'
    blockchain: 'base'
    blockchainid: 51