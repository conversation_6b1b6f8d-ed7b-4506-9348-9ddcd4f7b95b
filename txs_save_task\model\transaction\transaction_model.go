package transaction

import (
	"context"
	"fmt"
	"strings"
	"sync/atomic"
	"time"

	"github.com/bluele/gcache"
	log "github.com/cihub/seelog"
	"github.com/ethereum/go-ethereum/common"
	"github.com/tokenbankteam/txs_save_task/config"
	"github.com/tokenbankteam/txs_save_task/metrics"
	"github.com/tokenbankteam/txs_save_task/model"
	"github.com/tokenbankteam/txs_save_task/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Model struct {
	MongoClient         *mongo.Client
	TxDocument          *mongo.Collection
	PendingTxDocument   *mongo.Collection
	PendingTxDocumentV2 *mongo.Collection
	AppConfig           *config.AppConfig
	ExecutedTxCache     gcache.Cache

	// 统计字段
	TotalInserted int64 // 总插入数量
	TotalUpdated  int64 // 总更新数量
}

func NewModel(config *config.AppConfig) (*Model, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var mongoURI string
	if strings.HasPrefix(config.MongoAddr, "mongodb://") {
		mongoURI = config.MongoAddr
	} else {
		mongoURI = "mongodb://" + config.MongoAddr
	}

	// 配置MongoDB客户端选项
	clientOptions := options.Client().ApplyURI(mongoURI).
		SetMaxPoolSize(100).                         // 最大连接池大小
		SetMinPoolSize(10).                          // 最小连接池大小
		SetMaxConnIdleTime(30 * time.Minute).        // 连接空闲时间
		SetServerSelectionTimeout(10 * time.Second). // 服务器选择超时
		SetSocketTimeout(5 * time.Minute).           // Socket超时时间
		SetConnectTimeout(10 * time.Second)          // 连接超时时间

	mongoClient, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		log.Errorf("get mongo client error: %v", err)
		return nil, err
	}

	// Test the connection
	err = mongoClient.Ping(ctx, nil)
	if err != nil {
		log.Errorf("ping mongo error: %v", err)
		return nil, err
	}

	log.Infof("MongoDB连接成功: %s", mongoURI)

	var pendingDocV1, pendingDocV2 *mongo.Collection
	if config.PendingDatabase != "" && config.PendingDatabaseCollection != "" {
		pendingDocV1 = mongoClient.Database(config.PendingDatabase).Collection(config.PendingDatabaseCollection)
	}
	if config.PendingDatabase != "" && config.PendingDatabaseCollectionV2 != "" {
		pendingDocV2 = mongoClient.Database(config.PendingDatabase).Collection(config.PendingDatabaseCollectionV2)
	}

	return &Model{
		AppConfig:           config,
		MongoClient:         mongoClient,
		TxDocument:          mongoClient.Database(config.Database).Collection(config.Collection),
		PendingTxDocument:   pendingDocV1,
		PendingTxDocumentV2: pendingDocV2,
	}, nil
}

func (s *Model) UpdateTransactionForkInfo(msg *model.ForkMsg) (interface{}, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	filter := bson.M{
		"b": common.HexToHash(msg.BlockHash),
		"h": common.HexToHash(msg.TxHash),
	}
	update := bson.M{"$set": bson.M{"k": msg.Fork}}

	result, err := s.TxDocument.UpdateMany(ctx, filter, update)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *Model) InsertOrUpdateTransactionList(transactionList []*model.Transaction) (interface{}, error) {
	if len(transactionList) == 0 {
		return nil, nil
	}

	return s.insertOrUpdateInBlockTransactionList(transactionList)
}

func (s *Model) insertOrUpdateInBlockTransactionList(transactionList []*model.Transaction) (interface{}, error) {
	if len(transactionList) <= 0 {
		return nil, nil
	}

	// 增加超时时间到5分钟，处理大批次数据
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	start := time.Now()
	//log.Infof("开始处理批次，交易数量: %d", len(transactionList))

	var documents []interface{}
	for _, v := range transactionList {
		if v.Status == utils.Pending {
			continue
		}
		documents = append(documents, v)
	}

	if len(documents) == 0 {
		log.Infof("没有需要处理的操作（所有交易都是pending状态）")
		return nil, nil
	}

	// 设置批量插入选项，禁用有序插入以跳过重复记录
	opts := options.InsertMany().SetOrdered(false)
	result, err := s.TxDocument.InsertMany(ctx, documents, opts)

	var insertedCount int64 = 0
	if result != nil {
		insertedCount = int64(len(result.InsertedIDs))
	}

	if err != nil {
		// 只要批量插入报错，就改为逐条插入
		log.Warnf("批量插入遇到错误: %v, 已插入: %d, 改为逐条插入", err, insertedCount)

		// 逐条插入所有文档，如果遇到非重复错误则返回错误
		oneByOneInserted, oneByOneErr := s.insertDocumentsOneByOne(ctx, documents)
		if oneByOneErr != nil {
			return nil, oneByOneErr
		}
		insertedCount = oneByOneInserted
		log.Infof("逐条插入完成，总插入: %d, 总文档: %d", insertedCount, len(documents))
	} else {
		// 没有错误，所有记录都插入成功
		log.Debugf("批量插入完全成功，插入: %d条", insertedCount)
	}

	elapsed := (float64)(time.Since(start) / time.Millisecond)

	// 统计插入数量（insert操作只有插入，没有更新）
	batchInserted := insertedCount
	batchUpdated := int64(0)                              // insert操作没有更新
	skippedCount := int64(len(documents)) - insertedCount // 跳过的重复记录数量
	batchProcessed := batchInserted

	// 更新全局统计计数器
	atomic.AddInt64(&s.TotalInserted, batchInserted)
	atomic.AddInt64(&s.TotalUpdated, batchUpdated)

	// 获取累计统计
	totalInserted := atomic.LoadInt64(&s.TotalInserted)
	totalUpdated := atomic.LoadInt64(&s.TotalUpdated)
	totalProcessed := totalInserted + totalUpdated

	log.Infof("批量insert完成，耗时: %.2fms, 本批次[插入: %d, 跳过: %d, 处理: %d], 累计[插入: %d, 更新: %d, 总计: %d], 文档数: %d",
		elapsed, batchInserted, skippedCount, batchProcessed, totalInserted, totalUpdated, totalProcessed, len(documents))

	metrics.SaveTaskMongoWriteSummaryVec.
		WithLabelValues(fmt.Sprintf("%v", s.AppConfig.BlockChainId), "normal").
		Observe(elapsed)

	// 返回统计信息的结构体
	return map[string]interface{}{
		"InsertedCount": totalInserted,
		"UpdatedCount":  totalUpdated,
		"TotalCount":    totalProcessed,
		"DocumentCount": len(documents),
		"SkippedCount":  skippedCount,
		"ElapsedMs":     elapsed,
	}, nil
}

// GetStatistics 获取统计信息
func (s *Model) GetStatistics() map[string]int64 {
	totalInserted := atomic.LoadInt64(&s.TotalInserted)
	totalUpdated := atomic.LoadInt64(&s.TotalUpdated)
	return map[string]int64{
		"TotalInserted":  totalInserted,
		"TotalUpdated":   totalUpdated,
		"TotalProcessed": totalInserted + totalUpdated,
	}
}

// ResetStatistics 重置统计信息
func (s *Model) ResetStatistics() {
	atomic.StoreInt64(&s.TotalInserted, 0)
	atomic.StoreInt64(&s.TotalUpdated, 0)
}

// insertDocumentsOneByOne 逐条插入文档，忽略重复键错误，遇到非重复错误则返回错误
func (s *Model) insertDocumentsOneByOne(ctx context.Context, documents []interface{}) (int64, error) {
	var insertedCount int64 = 0

	for i, doc := range documents {
		// 为每条记录设置较短的超时时间
		singleCtx, cancel := context.WithTimeout(ctx, 10*time.Second)

		_, err := s.TxDocument.InsertOne(singleCtx, doc)
		cancel()

		if err != nil {
			if mongo.IsDuplicateKeyError(err) {
				// 重复键错误，跳过
				log.Debugf("逐条插入: 第%d条记录重复，跳过", i+1)
			} else {
				// 其他错误，返回错误
				log.Errorf("逐条插入: 第%d条记录插入失败: %v, 已成功插入: %d", i+1, err, insertedCount)
				return insertedCount, fmt.Errorf("逐条插入失败，第%d条记录错误: %v", i+1, err)
			}
		} else {
			// 插入成功
			insertedCount++
			if (i+1)%10 == 0 || i == len(documents)-1 {
				log.Debugf("逐条插入进度: %d/%d, 成功: %d", i+1, len(documents), insertedCount)
			}
		}
	}

	return insertedCount, nil
}

// Close 关闭MongoDB连接
func (s *Model) Close() error {
	if s.MongoClient != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		return s.MongoClient.Disconnect(ctx)
	}
	return nil
}
