package transaction

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/bluele/gcache"
	log "github.com/cihub/seelog"
	"github.com/ethereum/go-ethereum/common"
	"github.com/tokenbankteam/txs_save_task/config"
	"github.com/tokenbankteam/txs_save_task/metrics"
	"github.com/tokenbankteam/txs_save_task/model"
	"github.com/tokenbankteam/txs_save_task/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Model struct {
	MongoClient         *mongo.Client
	TxDocument          *mongo.Collection
	PendingTxDocument   *mongo.Collection
	PendingTxDocumentV2 *mongo.Collection
	AppConfig           *config.AppConfig
	ExecutedTxCache     gcache.Cache
}

func NewModel(config *config.AppConfig) (*Model, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var mongoURI string
	if strings.HasPrefix(config.MongoAddr, "mongodb://") {
		mongoURI = config.MongoAddr
	} else {
		mongoURI = "mongodb://" + config.MongoAddr
	}

	// 配置MongoDB客户端选项
	clientOptions := options.Client().ApplyURI(mongoURI).
		SetMaxPoolSize(100). // 最大连接池大小
		SetMinPoolSize(10). // 最小连接池大小
		SetMaxConnIdleTime(30 * time.Minute). // 连接空闲时间
		SetServerSelectionTimeout(10 * time.Second). // 服务器选择超时
		SetSocketTimeout(5 * time.Minute). // Socket超时时间
		SetConnectTimeout(10 * time.Second) // 连接超时时间

	mongoClient, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		log.Errorf("get mongo client error: %v", err)
		return nil, err
	}

	// Test the connection
	err = mongoClient.Ping(ctx, nil)
	if err != nil {
		log.Errorf("ping mongo error: %v", err)
		return nil, err
	}

	log.Infof("MongoDB连接成功: %s", mongoURI)

	var pendingDocV1, pendingDocV2 *mongo.Collection
	if config.PendingDatabase != "" && config.PendingDatabaseCollection != "" {
		pendingDocV1 = mongoClient.Database(config.PendingDatabase).Collection(config.PendingDatabaseCollection)
	}
	if config.PendingDatabase != "" && config.PendingDatabaseCollectionV2 != "" {
		pendingDocV2 = mongoClient.Database(config.PendingDatabase).Collection(config.PendingDatabaseCollectionV2)
	}

	return &Model{
		AppConfig:           config,
		MongoClient:         mongoClient,
		TxDocument:          mongoClient.Database(config.Database).Collection(config.Collection),
		PendingTxDocument:   pendingDocV1,
		PendingTxDocumentV2: pendingDocV2,
	}, nil
}

func (s *Model) UpdateTransactionForkInfo(msg *model.ForkMsg) (interface{}, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	filter := bson.M{
		"b": common.HexToHash(msg.BlockHash),
		"h": common.HexToHash(msg.TxHash),
	}
	update := bson.M{"$set": bson.M{"k": msg.Fork}}

	result, err := s.TxDocument.UpdateMany(ctx, filter, update)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *Model) InsertOrUpdateTransactionList(transactionList []*model.Transaction) (interface{}, error) {
	if len(transactionList) == 0 {
		return nil, nil
	}

	return s.insertOrUpdateInBlockTransactionList(transactionList)
}

func (s *Model) insertOrUpdateInBlockTransactionList(transactionList []*model.Transaction) (interface{}, error) {
	if len(transactionList) <= 0 {
		return nil, nil
	}

	// 增加超时时间到5分钟，处理大批次数据
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	start := time.Now()
	//log.Infof("开始处理批次，交易数量: %d", len(transactionList))

	var operations []mongo.WriteModel
	for _, v := range transactionList {
		if v.Status == utils.Pending {
			continue
		}

		filter := v.GetUniqueKeys()

		// 使用UpdateOne with upsert，性能比ReplaceOne更好
		updateDoc := bson.M{"$set": v}
		updateModel := mongo.NewUpdateOneModel()
		updateModel.SetFilter(filter)
		updateModel.SetUpdate(updateDoc)
		updateModel.SetUpsert(true)

		operations = append(operations, updateModel)
	}

	if len(operations) == 0 {
		log.Infof("没有需要处理的操作（所有交易都是pending状态）")
		return nil, nil
	}

	//log.Infof("准备执行批量upsert，操作数量: %d", len(operations))

	// 设置批量写入选项，禁用有序写入以提高性能
	opts := options.BulkWrite().SetOrdered(false)
	result, err := s.TxDocument.BulkWrite(ctx, operations, opts)
	if err != nil {
		log.Errorf("批量upsert失败: %v, 操作数量: %d", err, len(operations))
		return nil, err
	}

	elapsed := (float64)(time.Since(start) / time.Millisecond)
	log.Infof("批量upsert完成，耗时: %.2fms, 插入: %d, 更新: %d, 操作数: %d",
		elapsed, result.UpsertedCount, result.ModifiedCount, len(operations))

	metrics.SaveTaskMongoWriteSummaryVec.
		WithLabelValues(fmt.Sprintf("%v", s.AppConfig.BlockChainId), "normal").
		Observe(elapsed)

	return result, nil
}

// Close 关闭MongoDB连接
func (s *Model) Close() error {
	if s.MongoClient != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		return s.MongoClient.Disconnect(ctx)
	}
	return nil
}
