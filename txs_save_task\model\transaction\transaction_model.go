package transaction

import (
	"context"
	"fmt"
	"strings"
	"sync/atomic"
	"time"

	"github.com/bluele/gcache"
	log "github.com/cihub/seelog"
	"github.com/ethereum/go-ethereum/common"
	"github.com/tokenbankteam/txs_save_task/config"
	"github.com/tokenbankteam/txs_save_task/metrics"
	"github.com/tokenbankteam/txs_save_task/model"
	"github.com/tokenbankteam/txs_save_task/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Model struct {
	MongoClient         *mongo.Client
	TxDocument          *mongo.Collection
	PendingTxDocument   *mongo.Collection
	PendingTxDocumentV2 *mongo.Collection
	AppConfig           *config.AppConfig
	ExecutedTxCache     gcache.Cache

	// 统计字段
	TotalInserted int64 // 总插入数量
	TotalUpdated  int64 // 总更新数量
}

func NewModel(config *config.AppConfig) (*Model, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var mongoURI string
	if strings.HasPrefix(config.MongoAddr, "mongodb://") {
		mongoURI = config.MongoAddr
	} else {
		mongoURI = "mongodb://" + config.MongoAddr
	}

	// 配置MongoDB客户端选项
	clientOptions := options.Client().ApplyURI(mongoURI).
		SetMaxPoolSize(50).                         // 减少最大连接池大小，避免连接过多
		SetMinPoolSize(5).                          // 减少最小连接池大小
		SetMaxConnIdleTime(10 * time.Minute).       // 减少连接空闲时间
		SetServerSelectionTimeout(5 * time.Second). // 减少服务器选择超时
		SetSocketTimeout(30 * time.Second).         // 减少Socket超时时间
		SetConnectTimeout(5 * time.Second).         // 减少连接超时时间
		SetMaxConnecting(10)                        // 限制同时连接数

	mongoClient, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		log.Errorf("get mongo client error: %v", err)
		return nil, err
	}

	// Test the connection
	err = mongoClient.Ping(ctx, nil)
	if err != nil {
		log.Errorf("ping mongo error: %v", err)
		return nil, err
	}

	log.Infof("MongoDB连接成功: %s", mongoURI)

	var pendingDocV1, pendingDocV2 *mongo.Collection
	if config.PendingDatabase != "" && config.PendingDatabaseCollection != "" {
		pendingDocV1 = mongoClient.Database(config.PendingDatabase).Collection(config.PendingDatabaseCollection)
	}
	if config.PendingDatabase != "" && config.PendingDatabaseCollectionV2 != "" {
		pendingDocV2 = mongoClient.Database(config.PendingDatabase).Collection(config.PendingDatabaseCollectionV2)
	}

	return &Model{
		AppConfig:           config,
		MongoClient:         mongoClient,
		TxDocument:          mongoClient.Database(config.Database).Collection(config.Collection),
		PendingTxDocument:   pendingDocV1,
		PendingTxDocumentV2: pendingDocV2,
	}, nil
}

func (s *Model) UpdateTransactionForkInfo(msg *model.ForkMsg) (interface{}, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	filter := bson.M{
		"b": common.HexToHash(msg.BlockHash),
		"h": common.HexToHash(msg.TxHash),
	}
	update := bson.M{"$set": bson.M{"k": msg.Fork}}

	result, err := s.TxDocument.UpdateMany(ctx, filter, update)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *Model) InsertOrUpdateTransactionList(transactionList []*model.Transaction) (interface{}, error) {
	if len(transactionList) == 0 {
		return nil, nil
	}

	return s.insertOrUpdateInBlockTransactionList(transactionList)
}

func (s *Model) insertOrUpdateInBlockTransactionList(transactionList []*model.Transaction) (interface{}, error) {
	if len(transactionList) <= 0 {
		return nil, nil
	}

	start := time.Now()

	// 根据数量选择不同的超时时间
	var timeout time.Duration
	if len(transactionList) == 1 {
		timeout = 10 * time.Second // 单条记录用较短超时
	} else if len(transactionList) <= 10 {
		timeout = 30 * time.Second // 小批次用中等超时
	} else {
		timeout = 2 * time.Minute // 大批次用较长超时
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	var operations []mongo.WriteModel
	for _, v := range transactionList {
		if v.Status == utils.Pending {
			continue
		}

		filter := v.GetUniqueKeys()

		// 使用UpdateOne with upsert，性能比ReplaceOne更好
		updateDoc := bson.M{"$set": v}
		updateModel := mongo.NewUpdateOneModel()
		updateModel.SetFilter(filter)
		updateModel.SetUpdate(updateDoc)
		updateModel.SetUpsert(true)

		operations = append(operations, updateModel)
	}

	if len(operations) == 0 {
		log.Infof("没有需要处理的操作（所有交易都是pending状态）")
		return nil, nil
	}

	// 设置批量写入选项，禁用有序写入以提高性能
	opts := options.BulkWrite().SetOrdered(false)
	result, err := s.TxDocument.BulkWrite(ctx, operations, opts)
	if err != nil {
		log.Errorf("批量upsert失败: %v, 操作数量: %d", err, len(operations))
		return nil, err
	}

	elapsed := (float64)(time.Since(start) / time.Millisecond)

	// 统计插入和更新的总数量
	batchInserted := result.UpsertedCount
	batchUpdated := result.ModifiedCount
	batchMatched := result.MatchedCount

	// 如果UpsertedCount和ModifiedCount都是0，但MatchedCount不是0，说明数据完全相同
	if batchInserted == 0 && batchUpdated == 0 && batchMatched > 0 {
		// 将匹配但未修改的记录算作"更新"（实际上是无变化的更新）
		batchUpdated = batchMatched
	}

	batchProcessed := batchInserted + batchUpdated

	// 更新全局统计计数器
	atomic.AddInt64(&s.TotalInserted, batchInserted)
	atomic.AddInt64(&s.TotalUpdated, batchUpdated)

	// 获取累计统计
	totalInserted := atomic.LoadInt64(&s.TotalInserted)
	totalUpdated := atomic.LoadInt64(&s.TotalUpdated)
	totalProcessed := totalInserted + totalUpdated

	// 性能警告
	if len(operations) == 1 && elapsed > 200 {
		log.Warnf("单条记录处理耗时过长: %.2fms，可能存在性能问题", elapsed)
	} else if len(operations) > 1 && elapsed > float64(len(operations)*50) {
		log.Warnf("批量处理效率低: %.2fms for %d records (%.2fms/record)，可能存在性能问题",
			elapsed, len(operations), elapsed/float64(len(operations)))
	}

	log.Infof("批量upsert完成，耗时: %.2fms, 本批次[插入: %d, 更新: %d, 处理: %d], 累计[插入: %d, 更新: %d, 总计: %d], 操作数: %d",
		elapsed, batchInserted, batchUpdated, batchProcessed, totalInserted, totalUpdated, totalProcessed, len(operations))

	metrics.SaveTaskMongoWriteSummaryVec.
		WithLabelValues(fmt.Sprintf("%v", s.AppConfig.BlockChainId), "normal").
		Observe(elapsed)

	// 返回统计信息的结构体
	return map[string]interface{}{
		"InsertedCount":  totalInserted,
		"UpdatedCount":   totalUpdated,
		"TotalCount":     totalProcessed,
		"OperationCount": len(operations),
		"ElapsedMs":      elapsed,
	}, nil
}

// GetStatistics 获取统计信息
func (s *Model) GetStatistics() map[string]int64 {
	totalInserted := atomic.LoadInt64(&s.TotalInserted)
	totalUpdated := atomic.LoadInt64(&s.TotalUpdated)
	return map[string]int64{
		"TotalInserted":  totalInserted,
		"TotalUpdated":   totalUpdated,
		"TotalProcessed": totalInserted + totalUpdated,
	}
}

// ResetStatistics 重置统计信息
func (s *Model) ResetStatistics() {
	atomic.StoreInt64(&s.TotalInserted, 0)
	atomic.StoreInt64(&s.TotalUpdated, 0)
}

// Close 关闭MongoDB连接
func (s *Model) Close() error {
	if s.MongoClient != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		return s.MongoClient.Disconnect(ctx)
	}
	return nil
}
