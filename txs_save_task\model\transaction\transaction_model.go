package transaction

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/bluele/gcache"
	log "github.com/cihub/seelog"
	"github.com/ethereum/go-ethereum/common"
	"github.com/tokenbankteam/txs_save_task/config"
	"github.com/tokenbankteam/txs_save_task/metrics"
	"github.com/tokenbankteam/txs_save_task/model"
	"github.com/tokenbankteam/txs_save_task/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Model struct {
	MongoClient         *mongo.Client
	TxDocument          *mongo.Collection
	PendingTxDocument   *mongo.Collection
	PendingTxDocumentV2 *mongo.Collection
	AppConfig           *config.AppConfig
	ExecutedTxCache     gcache.Cache
}

func NewModel(config *config.AppConfig) (*Model, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var mongoURI string
	if strings.HasPrefix(config.MongoAddr, "mongodb://") {
		mongoURI = config.MongoAddr
	} else {
		mongoURI = "mongodb://" + config.MongoAddr
	}
	clientOptions := options.Client().ApplyURI(mongoURI)
	mongoClient, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		log.Errorf("get mongo client error: %v", err)
		return nil, err
	}

	// Test the connection
	err = mongoClient.Ping(ctx, nil)
	if err != nil {
		log.Errorf("ping mongo error: %v", err)
		return nil, err
	}

	var pendingDocV1, pendingDocV2 *mongo.Collection
	if config.PendingDatabase != "" && config.PendingDatabaseCollection != "" {
		pendingDocV1 = mongoClient.Database(config.PendingDatabase).Collection(config.PendingDatabaseCollection)
	}
	if config.PendingDatabase != "" && config.PendingDatabaseCollectionV2 != "" {
		pendingDocV2 = mongoClient.Database(config.PendingDatabase).Collection(config.PendingDatabaseCollectionV2)
	}

	return &Model{
		AppConfig:           config,
		MongoClient:         mongoClient,
		TxDocument:          mongoClient.Database(config.Database).Collection(config.Collection),
		PendingTxDocument:   pendingDocV1,
		PendingTxDocumentV2: pendingDocV2,
	}, nil
}

func (s *Model) UpdateTransactionForkInfo(msg *model.ForkMsg) (interface{}, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	filter := bson.M{
		"b": common.HexToHash(msg.BlockHash),
		"h": common.HexToHash(msg.TxHash),
	}
	update := bson.M{"$set": bson.M{"k": msg.Fork}}

	result, err := s.TxDocument.UpdateMany(ctx, filter, update)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *Model) InsertOrUpdateTransactionList(transactionList []*model.Transaction) (interface{}, error) {
	if len(transactionList) == 0 {
		return nil, nil
	}

	return s.insertOrUpdateInBlockTransactionList(transactionList)
}

func (s *Model) insertOrUpdateInBlockTransactionList(transactionList []*model.Transaction) (interface{}, error) {
	if len(transactionList) <= 0 {
		return nil, nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	start := time.Now()

	var operations []mongo.WriteModel
	for _, v := range transactionList {
		if v.Status == utils.Pending {
			continue
		}

		filter := v.GetUniqueKeys()

		upsertModel := mongo.NewReplaceOneModel()
		upsertModel.SetFilter(filter)
		upsertModel.SetReplacement(v)
		upsertModel.SetUpsert(true)

		operations = append(operations, upsertModel)
	}

	if len(operations) == 0 {
		return nil, nil
	}

	opts := options.BulkWrite().SetOrdered(false)
	result, err := s.TxDocument.BulkWrite(ctx, operations, opts)
	if err != nil {
		return nil, err
	}

	elapsed := (float64)(time.Since(start) / time.Millisecond)
	metrics.SaveTaskMongoWriteSummaryVec.
		WithLabelValues(fmt.Sprintf("%v", s.AppConfig.BlockChainId), "normal").
		Observe(elapsed)

	return result, nil
}

// Close 关闭MongoDB连接
func (s *Model) Close() error {
	if s.MongoClient != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		return s.MongoClient.Disconnect(ctx)
	}
	return nil
}
