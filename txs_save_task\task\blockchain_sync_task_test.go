package task

import (
	"testing"

	"github.com/Shopify/sarama"
	"github.com/stretchr/testify/assert"
)

func TestBatchOffsetTracker(t *testing.T) {
	// 测试基本的批次offset跟踪功能
	tracker := NewBatchOffsetTracker()

	// 创建测试消息
	msg1 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 100}
	msg2 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 101}
	msg3 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 1, Offset: 200}

	batch1 := []*sarama.ConsumerMessage{msg1, msg2}
	batch2 := []*sarama.ConsumerMessage{msg3}

	// 添加批次
	tracker.AddBatch(batch1)
	tracker.AddBatch(batch2)

	// 验证批次被添加
	assert.Equal(t, int64(100), tracker.partitionMinOffset["test-topic-0"])
	assert.Equal(t, int64(200), tracker.partitionMinOffset["test-topic-1"])

	// 测试可以提交的批次
	assert.True(t, tracker.CanCommitBatch(batch1))
	assert.True(t, tracker.CanCommitBatch(batch2))

	// 由于简化了架构，RemoveBatch现在是空方法，不需要测试移除逻辑
	// 测试重点是offset跟踪的正确性
}

func TestBatchOffsetTrackerSequentialCommit(t *testing.T) {
	// 测试顺序提交逻辑
	tracker := NewBatchOffsetTracker()

	// 创建有序的消息批次
	msg1 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 100}
	msg2 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 101}
	msg3 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 102}

	batch1 := []*sarama.ConsumerMessage{msg1}
	batch2 := []*sarama.ConsumerMessage{msg2}
	batch3 := []*sarama.ConsumerMessage{msg3}

	// 添加所有批次
	tracker.AddBatch(batch1)
	tracker.AddBatch(batch2)
	tracker.AddBatch(batch3)

	// batch3不能提交，因为有更早的批次在处理（batch1的offset=100 < batch3的minOffset=102）
	assert.False(t, tracker.CanCommitBatch(batch3))

	// batch2不能提交，因为有更早的批次在处理（batch1的offset=100 < batch2的minOffset=101）
	assert.False(t, tracker.CanCommitBatch(batch2))

	// batch1可以提交，因为没有比它更早的批次
	assert.True(t, tracker.CanCommitBatch(batch1))

	// 注意：由于简化了架构，RemoveBatch是空方法，所以后续的批次仍然不能提交
	// 这是设计上的权衡：partition数量有限，不主动清理
}

func TestBatchOffsetTrackerSelfExclusion(t *testing.T) {
	// 测试当前批次排除自己的逻辑
	tracker := NewBatchOffsetTracker()

	// 创建一个批次包含多个offset
	msg1 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 100}
	msg2 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 101}
	msg3 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 102}

	batch := []*sarama.ConsumerMessage{msg1, msg2, msg3}

	// 添加批次
	tracker.AddBatch(batch)

	// 这个批次应该可以提交自己，因为没有其他更早的批次
	assert.True(t, tracker.CanCommitBatch(batch))

	// 添加另一个更晚的批次
	msg4 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 103}
	msg5 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 104}
	batch2 := []*sarama.ConsumerMessage{msg4, msg5}

	tracker.AddBatch(batch2)

	// 第一个批次仍然可以提交
	assert.True(t, tracker.CanCommitBatch(batch))

	// 第二个批次不能提交，因为第一个批次还在处理
	assert.False(t, tracker.CanCommitBatch(batch2))
}

func TestBatchOffsetTrackerConcurrency(t *testing.T) {
	// 测试并发安全性
	tracker := NewBatchOffsetTracker()

	// 并发添加批次
	done := make(chan bool, 10)
	for i := 0; i < 10; i++ {
		go func(offset int64) {
			msg := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: offset}
			batch := []*sarama.ConsumerMessage{msg}
			tracker.AddBatch(batch)
			done <- true
		}(int64(100 + i))
	}

	// 等待所有goroutine完成
	for i := 0; i < 10; i++ {
		<-done
	}

	// 验证最小offset被正确设置
	assert.Equal(t, int64(100), tracker.partitionMinOffset["test-topic-0"])

	// 测试并发读取
	for i := 0; i < 5; i++ {
		go func() {
			msg := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 105}
			batch := []*sarama.ConsumerMessage{msg}
			tracker.CanCommitBatch(batch)
			done <- true
		}()
	}

	for i := 0; i < 5; i++ {
		<-done
	}
}

func TestBlockChainSyncTaskBatchManagement(t *testing.T) {
	// 创建模拟的消息
	msg1 := &sarama.ConsumerMessage{
		Topic:     "test-topic",
		Partition: 0,
		Offset:    100,
	}

	msg2 := &sarama.ConsumerMessage{
		Topic:     "test-topic",
		Partition: 1,
		Offset:    200,
	}

	msg3 := &sarama.ConsumerMessage{
		Topic:     "another-topic",
		Partition: 0,
		Offset:    300,
	}

	// 创建BlockChainSyncTask实例（需要模拟依赖）
	task := &BlockChainSyncTask{
		batchTracker: NewBatchOffsetTracker(),
	}

	// 测试批次管理
	batch1 := []*sarama.ConsumerMessage{msg1, msg2}
	batch2 := []*sarama.ConsumerMessage{msg3}

	// 添加批次
	task.batchTracker.AddBatch(batch1)
	task.batchTracker.AddBatch(batch2)

	// 验证批次可以提交
	assert.True(t, task.batchTracker.CanCommitBatch(batch1))
	assert.True(t, task.batchTracker.CanCommitBatch(batch2))

	// 验证批次状态（简化架构下不需要移除测试）
	assert.True(t, task.batchTracker.CanCommitBatch(batch2))
}

func BenchmarkBatchOffsetTracker(b *testing.B) {
	tracker := NewBatchOffsetTracker()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		msg := &sarama.ConsumerMessage{
			Topic:     "test-topic",
			Partition: 0,
			Offset:    int64(i),
		}
		batch := []*sarama.ConsumerMessage{msg}

		tracker.AddBatch(batch)
		if i%100 == 0 {
			tracker.CanCommitBatch(batch)
		}
	}
}
